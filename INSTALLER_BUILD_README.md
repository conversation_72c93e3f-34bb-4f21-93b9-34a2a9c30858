# SorcerioModules Professional Windows Installer

## Overview

This document describes the professional Windows installer creation process for SorcerioModules, a comprehensive social media automation tool developed by Ozmorph.

## Installer Features

### ✅ Professional Standards
- **Company**: Ozmorph
- **Application**: Sorcerio
- **Version**: 1.0.0
- **Target**: Windows 10/11 (64-bit)
- **Size**: ~200-500 MB (complete standalone)

### ✅ Commercial-Grade Features
- Complete standalone installation (no external dependencies)
- Professional setup wizard with "Next/Next" flow
- License agreement and user acceptance dialogs
- Automatic desktop and Start Menu shortcuts
- Complete uninstaller with registry cleanup
- Modern Windows installer standards compliance
- Ready for digital signing and commercial distribution

### ✅ Technical Specifications
- **Build System**: PyInstaller + Inno Setup
- **Python Runtime**: Embedded (no separate installation required)
- **Dependencies**: All bundled (PyQt5, Selenium, FFmpeg, etc.)
- **Assets**: All icons, configurations, and resources included
- **Chrome**: Portable Chromium browser included
- **Installation**: Silent or interactive modes supported

## Build Process

### Prerequisites
1. **Windows 10/11** (64-bit)
2. **Python 3.8+** installed with PATH access
3. **Internet connection** (for downloading build tools)
4. **Administrator privileges** (for installer creation)

### Quick Start
1. Open Command Prompt as Administrator
2. Navigate to the SorcerioModules project directory
3. Run: `build_installer.bat`
4. Wait for the build process to complete
5. Find your installer in: `C:\Users\<USER>\Desktop\sorcsetup\`

### Manual Build Process
If you prefer to run the build manually:

```bash
# Install build dependencies
python -m pip install --upgrade pip
python -m pip install pyinstaller requests

# Run the installer builder
python pyinstaller_builder.py
```

## Build Components

### 1. PyInstaller Application Bundle
- Bundles Python runtime and all dependencies
- Creates standalone executable
- Includes all project files and assets
- Optimized for size and performance

### 2. Inno Setup Installer
- Professional Windows installer creation
- Modern wizard interface
- License agreement handling
- Registry integration
- Uninstaller generation

### 3. Included Dependencies
- **GUI Framework**: PyQt5, PyQtWebEngine
- **Browser Automation**: Selenium, Chrome WebDriver
- **Media Processing**: MoviePy, OpenCV, Pillow
- **Social Media APIs**: Instagrapi, Instaloader, yt-dlp
- **Task Scheduling**: APScheduler
- **Networking**: Requests, BeautifulSoup4
- **System Integration**: Win32, PSUtil

### 4. Bundled Assets
- Application icons (Instagram, Twitter/X)
- Portable Chromium browser
- Configuration templates
- Video storage directories
- Live feed thumbnails
- Documentation files

## Output Structure

The installer creates the following structure on target machines:

```
C:\Program Files\Ozmorph\Sorcerio\
├── Sorcerio.exe                 # Main application
├── _internal\                   # Python runtime and dependencies
├── configuration\               # User profiles and settings
├── videos\                      # Downloaded content storage
├── portable_chromium\           # Bundled browser
├── live_feed_thumbnails\        # UI assets
├── instagram.ico               # Platform icons
├── x.ico
└── README.md                   # Documentation
```

## Installation Features

### User Experience
1. **Welcome Screen**: Professional branded welcome
2. **License Agreement**: Legal terms and conditions
3. **Installation Directory**: Customizable install location
4. **Progress Indicator**: Real-time installation progress
5. **Completion**: Launch option and shortcuts creation

### System Integration
- **Desktop Shortcut**: Quick access icon
- **Start Menu**: Professional program group
- **Registry Entries**: Proper Windows integration
- **Uninstaller**: Complete removal capability
- **File Associations**: (Optional) Media file handling

## Commercial Distribution

### Ready for Sale
The generated installer is immediately ready for:
- **Website Distribution**: Upload and sell directly
- **Software Marketplaces**: Submit to app stores
- **Enterprise Deployment**: Corporate installations
- **Retail Distribution**: Physical or digital sales

### Digital Signing (Recommended)
For enhanced trust and security:
1. Obtain a code signing certificate
2. Sign the installer executable
3. Verify signature before distribution

### Marketing Benefits
- Professional appearance builds customer trust
- Zero technical support for installation issues
- Automatic updates capability (future enhancement)
- Enterprise-grade deployment options

## Troubleshooting

### Common Build Issues
1. **Python Not Found**: Ensure Python is in PATH
2. **Permission Denied**: Run as Administrator
3. **Internet Required**: Build downloads components
4. **Disk Space**: Ensure 2GB+ free space

### Build Logs
Check the console output for detailed build information and any error messages.

### Support
For build issues or questions:
- Check the console output for error details
- Ensure all prerequisites are met
- Verify internet connectivity
- Run with Administrator privileges

## Version History

### v1.0.0 (Current)
- Initial professional installer release
- Complete standalone distribution
- PyInstaller + Inno Setup build system
- Commercial-grade features and standards

## License

This installer builder is part of the SorcerioModules project.
Copyright (c) 2024 Ozmorph. All rights reserved.

---

**Ready to build your professional Windows installer? Run `build_installer.bat` now!**
