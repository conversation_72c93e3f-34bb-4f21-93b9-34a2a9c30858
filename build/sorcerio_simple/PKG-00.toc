('C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\Sorcerio.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\main.py',
   'PYSOURCE'),
  ('portable_chromium\\browser\\chrome-win\\D3DCompiler_47.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\D3DCompiler_47.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\chrome.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\chrome.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome.exe',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\chrome_elf.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome_elf.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\chrome_proxy.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome_proxy.exe',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\chrome_pwa_launcher.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome_pwa_launcher.exe',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\chrome_wer.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome_wer.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\elevation_service.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\elevation_service.exe',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\eventlog_provider.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\eventlog_provider.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\interactive_ui_tests.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\interactive_ui_tests.exe',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\libEGL.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\libEGL.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\libGLESv2.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\libGLESv2.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\mojo_core.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\mojo_core.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\notification_helper.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\notification_helper.exe',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\vk_swiftshader.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\vk_swiftshader.dll',
   'BINARY'),
  ('portable_chromium\\browser\\chrome-win\\vulkan-1.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\vulkan-1.dll',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\chromedriver.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\chromedriver.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_1f2abcb1d6da4e2da38acc497cec2dcc.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_1f2abcb1d6da4e2da38acc497cec2dcc.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_270ac38c8c9040c99e86d0228bd893f7.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_270ac38c8c9040c99e86d0228bd893f7.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_7aa5b36504b24532bce5cdd1452bc48e.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_7aa5b36504b24532bce5cdd1452bc48e.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_8d703000b8b94fa59d1d0c6aa5d32e43.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_8d703000b8b94fa59d1d0c6aa5d32e43.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_8e92a351fe2a4facaeb0e0217ec95182.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_8e92a351fe2a4facaeb0e0217ec95182.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_912f16a86df84befa533d73e555f9b2b.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_912f16a86df84befa533d73e555f9b2b.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_9b6b775f20af426a947d2f0d86d9b70a.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_9b6b775f20af426a947d2f0d86d9b70a.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_9dbf94ad2b6b49de956bfc4e398e77f8.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_9dbf94ad2b6b49de956bfc4e398e77f8.exe',
   'BINARY'),
  ('portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_da9436447d3542448569eb95570dd06d.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\runtime\\chromedriver_da9436447d3542448569eb95570dd06d.exe',
   'BINARY'),
  ('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmlfolderlistmodelplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmlfolderlistmodelplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\particlesplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\particlesplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\widgetsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\widgetsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\qtremoteobjects.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\qtremoteobjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qtquickextrasflatplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qtquickextrasflatplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\declarative_positioning.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\declarative_positioning.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\labsanimationplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\labsanimationplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qtquickextrasplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qtquickextrasplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmllocalstorageplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmllocalstorageplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmlxmllistmodelplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmlxmllistmodelplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qtquickcontrols2materialstyleplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qtquickcontrols2materialstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qtquickcontrols2universalstyleplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qtquickcontrols2universalstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\declarative_qmlwebsockets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\declarative_qmlwebsockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\sharedimageplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\sharedimageplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\windowplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\windowplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\dialogsprivateplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\dialogsprivateplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qtgraphicaleffectsprivate.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qtgraphicaleffectsprivate.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\qmlplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\qmlplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qtquickcontrols2imaginestyleplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qtquickcontrols2imaginestyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\labsmodelsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\labsmodelsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qtqmlstatemachine.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qtqmlstatemachine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\declarative_webview.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\declarative_webview.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qtlabsplatformplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qtlabsplatformplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qtlabscalendarplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qtlabscalendarplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qtquicktemplates2plugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qtquicktemplates2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\qquick3dplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\qquick3dplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\declarative_sensors.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\declarative_sensors.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qtquicktimelineplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qtquicktimelineplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qtqmlremoteobjects.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qtqmlremoteobjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtTest\\qmltestplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\qmltestplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qtquick3dmaterialplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qtquick3dmaterialplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\modelsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\modelsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qtquick3dhelpersplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qtquick3dhelpersplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qtquick3deffectplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qtquick3deffectplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\declarative_location.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\declarative_location.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\workerscriptplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\workerscriptplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\declarative_nfc.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\declarative_nfc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qtquickcontrolsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qtquickcontrolsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmlsettingsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmlsettingsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qtquickcontrols2plugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qtquickcontrols2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmlwavefrontmeshplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmlwavefrontmeshplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\qtwebengineplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\qtwebengineplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qquicklayoutsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qquicklayoutsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\locationlabsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\locationlabsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\qtquick2plugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\qtquick2plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qtquickcontrols2fusionstyleplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qtquickcontrols2fusionstyleplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qtgraphicaleffectsplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qtgraphicaleffectsplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\declarative_webchannel.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\declarative_webchannel.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmlshapesplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmlshapesplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\dialogplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\dialogplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\declarative_bluetooth.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\declarative_bluetooth.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\QtWebEngineProcess.exe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\QtWebEngineProcess.exe',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQml.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtQml.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPrintSupport.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtPrintSupport.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWebEngineCore.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtWebEngineCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQuick.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtQuick.pyd',
   'EXTENSION'),
  ('PyQt5\\QtPositioning.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtPositioning.pyd',
   'EXTENSION'),
  ('PyQt5\\QtNetwork.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWebChannel.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtWebChannel.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWebEngineWidgets.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtWebEngineWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtQuickWidgets.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtQuickWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('chrome_elf.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome_elf.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebEngine.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebEngine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebEngineCore.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebEngineCore.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python3.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebEngineWidgets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebEngineWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'BINARY'),
  ('LICENSE.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\LICENSE.txt',
   'DATA'),
  ('configuration\\instagram\\profile1.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\instagram\\profile1.json',
   'DATA'),
  ('configuration\\instagram\\profile2.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\instagram\\profile2.json',
   'DATA'),
  ('configuration\\instagram\\profile3.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\instagram\\profile3.json',
   'DATA'),
  ('configuration\\instagram\\profile4.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\instagram\\profile4.json',
   'DATA'),
  ('configuration\\instagram\\profile5.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\instagram\\profile5.json',
   'DATA'),
  ('configuration\\instagram\\profile6.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\instagram\\profile6.json',
   'DATA'),
  ('configuration\\instagram\\profile7.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\instagram\\profile7.json',
   'DATA'),
  ('configuration\\instagram\\profile8.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\instagram\\profile8.json',
   'DATA'),
  ('configuration\\istatistikler.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\istatistikler.json',
   'DATA'),
  ('configuration\\twitter\\profile1.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\profile1.json',
   'DATA'),
  ('configuration\\twitter\\profile2.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\profile2.json',
   'DATA'),
  ('configuration\\twitter\\profile3.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\profile3.json',
   'DATA'),
  ('configuration\\twitter\\profile4.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\profile4.json',
   'DATA'),
  ('configuration\\twitter\\profile5.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\profile5.json',
   'DATA'),
  ('configuration\\twitter\\profile6.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\profile6.json',
   'DATA'),
  ('configuration\\twitter\\profile7.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\profile7.json',
   'DATA'),
  ('configuration\\twitter\\profile8.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\profile8.json',
   'DATA'),
  ('configuration\\twitter\\sessions\\BuzzHaber_session.meta.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\sessions\\BuzzHaber_session.meta.json',
   'DATA'),
  ('configuration\\twitter\\sessions\\twitter_stats_session.meta.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\configuration\\twitter\\sessions\\twitter_stats_session.meta.json',
   'DATA'),
  ('instagram.ico',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\instagram.ico',
   'DATA'),
  ('ozmorph_logo.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\ozmorph_logo.png',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\113.0.5620.0.manifest',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\113.0.5620.0.manifest',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\Dictionaries\\en-US-10-1.bdic',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\Dictionaries\\en-US-10-1.bdic',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\First Run',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\First '
   'Run',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\MEIPreload\\manifest.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\MEIPreload\\manifest.json',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\MEIPreload\\preloaded_data.pb',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\MEIPreload\\preloaded_data.pb',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\chrome_100_percent.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome_100_percent.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\chrome_200_percent.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome_200_percent.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\icudtl.dat',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\icudtl.dat',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\af.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\af.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\am.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\am.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ar-XB.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ar-XB.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ar.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ar.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\bg.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\bg.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\bn.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\bn.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ca.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ca.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\cs.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\cs.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\da.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\da.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\de.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\de.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\el.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\el.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\en-GB.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\en-GB.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\en-US.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\en-US.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\en-XA.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\en-XA.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\es-419.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\es-419.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\es.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\es.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\et.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\et.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\fa.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\fa.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\fi.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\fi.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\fil.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\fil.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\fr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\fr.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\gu.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\gu.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\he.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\he.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\hi.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\hi.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\hr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\hr.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\hu.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\hu.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\id.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\id.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\it.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\it.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ja.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ja.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\kn.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\kn.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ko.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ko.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\lt.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\lt.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\lv.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\lv.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ml.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ml.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\mr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\mr.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ms.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ms.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\nb.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\nb.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\nl.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\nl.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\pl.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\pl.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\pt-BR.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\pt-BR.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\pt-PT.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\pt-PT.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ro.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ro.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ru.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ru.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\sk.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\sk.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\sl.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\sl.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\sr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\sr.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\sv.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\sv.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\sw.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\sw.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ta.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ta.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\te.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\te.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\th.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\th.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\tr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\tr.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\uk.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\uk.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\ur.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\ur.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\vi.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\vi.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\zh-CN.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\zh-CN.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\locales\\zh-TW.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\locales\\zh-TW.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\nacl_irt_x86_64.nexe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\nacl_irt_x86_64.nexe',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\resources.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\resources.pak',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\v8_context_snapshot.bin',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\v8_context_snapshot.bin',
   'DATA'),
  ('portable_chromium\\browser\\chrome-win\\vk_swiftshader_icd.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\vk_swiftshader_icd.json',
   'DATA'),
  ('portable_chromium\\driver\\chromedriver_win32\\LICENSE.chromedriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\driver\\chromedriver_win32\\LICENSE.chromedriver',
   'DATA'),
  ('videos\\social_downloader.log',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\videos\\social_downloader.log',
   'DATA'),
  ('x.ico',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\x.ico',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\RECORD',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\APScheduler-3.11.0.dist-info\\REQUESTED',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\APScheduler-3.11.0.dist-info\\METADATA',
   'DATA'),
  ('tzdata-2025.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata-2025.2.dist-info\\METADATA',
   'DATA'),
  ('tzdata-2025.2.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata-2025.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\APScheduler-3.11.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\APScheduler-3.11.0.dist-info\\WHEEL',
   'DATA'),
  ('tzdata-2025.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata-2025.2.dist-info\\INSTALLER',
   'DATA'),
  ('tzdata-2025.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata-2025.2.dist-info\\WHEEL',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\APScheduler-3.11.0.dist-info\\INSTALLER',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\APScheduler-3.11.0.dist-info\\top_level.txt',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\INSTALLER',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('tzdata-2025.2.dist-info\\licenses\\licenses\\LICENSE_APACHE',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata-2025.2.dist-info\\licenses\\licenses\\LICENSE_APACHE',
   'DATA'),
  ('tzdata-2025.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata-2025.2.dist-info\\top_level.txt',
   'DATA'),
  ('tzdata-2025.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata-2025.2.dist-info\\RECORD',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\APScheduler-3.11.0.dist-info\\entry_points.txt',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\METADATA',
   'DATA'),
  ('APScheduler-3.11.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\APScheduler-3.11.0.dist-info\\RECORD',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\WHEEL',
   'DATA'),
  ('tzlocal-5.3.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal-5.3.1.dist-info\\top_level.txt',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tzdata\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Turkey',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tzdata\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tzdata\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tzdata\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Japan',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tzdata\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Iceland',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('tzdata\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Recife',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('tzdata\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\MET',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Egypt',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Lima',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\MST',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tzdata\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\ROC',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tzdata\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tzdata\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\WET',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tzdata\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Poland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tzdata\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tzdata\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\EST',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coyhaique',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tzdata\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Israel',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('tzdata\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\NZ',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Libya',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Belize',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tzdata\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tzdata\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\ROK',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('tzdata\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\CET',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tzdata\\zones',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zones',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tzdata\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Navajo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tzdata\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Factory',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('tzdata\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tzdata\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Portugal',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tzdata\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Cuba',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tzdata\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\PRC',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('tzdata\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\leapseconds',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\New_York',
   'DATA'),
  ('tzdata\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Hongkong',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tzdata\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tzdata\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\zone.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tzdata\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\HST',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tzdata\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\GB',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\W-SU',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tzdata\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\EET',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Denver',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Iran',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tzdata\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cv2\\config.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config-3.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('selenium\\py.typed',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v134\\py.typed',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v134\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v135\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassSinglePassMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassSinglePassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\warning.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\warning.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_b.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_b.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PieMenuSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-right.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-right.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\Object3DSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\Object3DSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ContainerSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ContainerSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\FocusFrameStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\HDRBloomTonemap.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\HDRBloomTonemap.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CursorDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CursorDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ComboBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ScrollViewSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ScrollViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_full_contrast.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_full_contrast.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\IdComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\IdComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Colorize.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Colorize.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-vertical.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-vertical.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Flip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Flip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\GaussianBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\GaussianBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\white.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\white.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBarItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\SignalSpy.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\SignalSpy.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Vignette.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Vignette.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\stackview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\ConfirmDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\ConfirmDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ColorDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ColorDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\window_border.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\window_border.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\rightanglearrow.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\rightanglearrow.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ThresholdMask.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ThresholdMask.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\custommaterial.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SystemPaletteSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\MaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\MaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\location\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\location\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialGradient.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\CopperMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\CopperMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSphere.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSphere.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBarItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\header.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\header.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFileDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TreeViewItemDelegateLoader.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.jsc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\style.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_d.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\grunge_d.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBarItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FlipSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedEmissiveMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumAnodizedEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\BusyIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\VignetteSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\source\\custommaterial_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\source\\custommaterial_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\dummy16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\meshes\\axisGrid.mesh',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\meshes\\axisGrid.mesh',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RectangularGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RectangularGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\WasdController.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\WasdController.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderGroove.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderGroove.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Shapes\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\StatusBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\view3D_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\view3D_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\AuthenticationDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\AuthenticationDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EffectSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_normal.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_normal.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RadialBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\BrightnessContrast.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\BrightnessContrast.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\LabelSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\LabelSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\AxisHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\AxisHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TabViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ActionGroup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ActionGroup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\PromptDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\PromptDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextField.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\HoverButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-down.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-down.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularGaugeStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StatusBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Scatter.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Scatter.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-groove.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-groove.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\question.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\question.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DelayButtonSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\TextureSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Blend.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Blend.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\ToggleButtonSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\BusyIndicator.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-up.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-up.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\StackViewSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\StackViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\AlertDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\AlertDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBarItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBarItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ProgressBarSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ProgressBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\shadow.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\shadow.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\MaskedBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\MaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Glow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Glow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\tumbler-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\critical.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\critical.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.jsc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\AdditiveColorGradient.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\AdditiveColorGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\PieMenu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\qtquickextras.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\qtquickextras.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GammaAdjust.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GammaAdjust.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DepthOfFieldHQBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\togglebutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Blur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Blur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\CircularGauge.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\OpacityMask.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\OpacityMask.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RadioButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\spinbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DelayButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DelayButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DropShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DropShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFileDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularTickmarkLabel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CheckBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\BusyIndicatorSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\BusyIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\question.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\question.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageIndicatorSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PageIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ComboBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\testlogger.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\testlogger.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ScrollView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\needle.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\needle.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\delaybutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ScatterSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_a.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\brushed_a.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextHandle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Desaturate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Desaturate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\gauge-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive_mask.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive_mask.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\effectlib.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\effectlib.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\ColorSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\AbstractButtonSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\AbstractButtonSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\plane_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\plane_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\button-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\TestCase.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\TestCase.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DirectionalLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumEmissiveMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\swipeview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\sphere_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\sphere_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferBlitSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\dial-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DirectionalBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\DirectionalBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\progressbar-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DelayButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Drawer.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Drawer.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\RemoteObjects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckBoxSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebChannel\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebChannel\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Desaturate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Desaturate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Control.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Control.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_diffuse.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_diffuse.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient3D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient3D.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\editbox.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\editbox.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BoxShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\BoxShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\model16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\model16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\NodeSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_large.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_large.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\slider-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\materiallib.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\materiallib.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\MenuBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtTest\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtTest\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtSensors\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtSensors\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\MotionBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\MotionBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\plane.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\PromptDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\PromptDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\HueSaturation.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\HueSaturation.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumBrushedMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\AluminumBrushedMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\ToggleButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SliderSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SliderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Fxaa.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Fxaa.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\CheckBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Gauge.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\FilePicker.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\FilePicker.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\View3DSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\StatusIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassRefractiveMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\GlassRefractiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentScroller.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\XmlListModel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_trans.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\paper_trans.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SliderStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionRippleSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Emboss.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\Emboss.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\AuthenticationDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\AuthenticationDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SliderStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\DefaultWindowDecoration.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_small.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_small.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RectangularGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RectangularGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SpinBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CustomMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TextInputWithHandles.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TreeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ItemDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ItemDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_lv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\TextSingleton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\DialSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtLocation\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtLocation\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\quick3d.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\quick3d.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassRefractiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\sunken_frame.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\sunken_frame.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\platform\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewTransition.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\GroupBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\SourceProxy.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Flat\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient1D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient1D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewItemDelegateLoader.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedEmissiveMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PlasticStructuredRedEmissiveMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Label.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PointLightSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\DialStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient4D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient4D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperOfficeMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperOfficeMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ApplicationWindowStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-horizontal.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-horizontal.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\FocusFrameStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\StackViewDelegate.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\GaussianBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\tumbler-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\SCurveTonemap.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\SCurveTonemap.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PlasticStructuredRedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PerspectiveCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\dial-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianInnerShadow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaddingSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaddingSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebSockets\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebSockets\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkers.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkers.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ModelSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CircularTickmarkLabelStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\settings\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\AlertDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\AlertDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ConfirmDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\ConfirmDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\crosshairs.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\crosshairs.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\PieMenuIcon.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\source\\effect_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\source\\effect_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\AreaLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabBarSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TabBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Calendar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultFontDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FastGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\folderlistmodel\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LevelAdjust.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LevelAdjust.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ColorOverlay.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ColorOverlay.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SetUniformValueSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TabView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableViewColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\rangeslider-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolseparator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianMaskedBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CalendarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ChromaticAberrationSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Container.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Container.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextAreaStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Slider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Slider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextFieldStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\statusindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\InnerShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\InnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperArtisticMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\PaperArtisticMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\CheckSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\spherical_checker.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\spherical_checker.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cube.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\MotionBlurSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EmbossSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\view3D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ProgressBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetColorDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\SteelMilledConcentricMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\SteelMilledConcentricMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\qtquickcontrols2.metainfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\qtquickcontrols2.metainfo',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultMessageDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CalendarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cube_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cube_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TableView.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SpinBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SpinBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ElevationEffect.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ElevationEffect.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ControlSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\roundbutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Popup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Popup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\slider_handle.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\slider_handle.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Menu.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\TouchSelectionMenu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\TouchSelectionMenu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\TumblerColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\RadioButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Pane.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Pane.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\AdditiveColorGradientSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Slider.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionRipple.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionRipple.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\FocusFrame.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShadowSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShadowSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\PaneSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Label.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Label.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSpiralSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkmark.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\checkmark.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TableViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\Handle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\groupbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PassSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\piemenu-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Control.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cylinder_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cylinder_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextFieldSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextFieldSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\PictureSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\IconGlyph.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\VerticalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\VerticalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\CheckBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Displace.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\Displace.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\StateMachine\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DialogButtonBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DialogButtonBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DefaultMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Particles.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SwipeView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Action.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Action.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\switch-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\HDRBloomTonemapSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtPositioning\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtPositioning\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\TouchHandle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls1Delegates\\TouchHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\RenderStateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ConicalGradient.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ConicalGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\wavefrontmesh\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\copy.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\copy.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\combobox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LinearGradient.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\LinearGradient.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TextFieldStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwitchDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\DayOfWeekRow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GaussianBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\GaussianBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\IdComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\IdComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SpotLightSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSpiral.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DistortionSpiral.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textfield-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeViewSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SwipeViewSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TreeViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetFontDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Menu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Menu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\radiobutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\FrameSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\FrameSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-left.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\arrow-left.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\GroupBox.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BlendingSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\CheckBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\CopperMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultDialogWrapper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ScrollBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuContentItem.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\test\\qtestroot\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwipeDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwipeDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ComboBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ComboBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToolBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RadioDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pane-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\AbstractButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\AbstractButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient2D.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\randomGradient2D.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shadercommand16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\light16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\picture-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\OrthographicCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TableViewSelection.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SpinBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Templates.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\emissive.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtNfc\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtNfc\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RadioButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\texture16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarHeaderModel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ToolButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ComboBoxSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ComboBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Window.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\GaugeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\WorkerScript.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-handle.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\slider-handle.png',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\DelayButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextAreaSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TextAreaSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtRemoteObjects\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtRemoteObjects\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\icons.ttf',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\qml\\icons.ttf',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\HandleStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\AbstractCheckable.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\Style.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\EdgeDetect.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\EdgeDetect.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button_down.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\button_down.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cone.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TumblerSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\TumblerSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastInnerShadow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.js',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\CalendarUtils.js',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumBrushedMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\sphere16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbar-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\MenuItem.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\MenuItem.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\MonthGrid.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\SwitchDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\SteelMilledConcentricMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\CheckDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackViewSlideDelegate.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SpinBoxSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\SpinBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel_aniso.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel_aniso.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ChromaticAberration.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ChromaticAberration.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\WidgetMessageDialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\material.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\StatusIndicatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RoundButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\RoundButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CustomCameraSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\MenuItemSubControls.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\AluminumAnodizedEmissiveMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\frame-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DepthOfFieldHQBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\DepthOfFieldHQBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ProgressBarStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperArtisticMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\WebSockets\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\WebSockets\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextField.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\TextField.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cone_model_template.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\source\\cone_model_template.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GaugeStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-transient.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\scrollbar-handle-transient.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolBarSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolBarSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\information.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\Controls2Delegates\\information.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\TextArea.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Frame.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Frame.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\EdgeDetectSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\FxaaSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ButtonPanel.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ButtonPanel.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastMaskedBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ColumnMenuContent.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ScrollViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtdeclarative_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtdeclarative_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_trans.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\art_paper_trans.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\TiltShiftSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TabViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\focusframe.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\focusframe.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebView\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebView\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\DepthInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\WeekNumberColumn.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ButtonSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\RowItemSingleton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\BusyIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\BusyIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\BrushStrokes.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\BrushStrokes.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\PieMenuStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\PageIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\PageIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\DialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Switch.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu_base.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\information.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\images\\information.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ScrollViewStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\SceneEnvironmentSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SwitchStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Layouts\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\Private\\CircularButtonStyleHelper.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\FastGlow.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\FastBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\FastBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\calendar\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\pageindicator-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderInfoSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TextAreaStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\StackView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\StackView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\ScrollIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\EditMenu.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\effect.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RangeSliderSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RangeSliderSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtBluetooth\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtBluetooth\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\checkbox-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\TumblerStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\MenuBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\CircularGaugeSpecifics.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\check.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\check.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\CommonStyleHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RecursiveBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\RecursiveBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Extras\\designer\\images\\circulargauge-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\RadioButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ApplicationWindow.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ApplicationWindow.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ZoomBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\ZoomBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\TabBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ApplicationWindowStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SliderHandle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\SliderHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dialog.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\Dialog.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioIndicator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RadioIndicator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\SwitchStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ComboBoxStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\TabButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\scrollview-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Page.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Page.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ItemDelegateSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\TiltShift.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\TiltShift.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ContentItem.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Timeline\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\GlassMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Universal\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\BufferInputSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ColorMaster.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\ColorMaster.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\sharedimage\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\ToggleButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Dialogs\\DefaultColorDialog.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\GroupBox.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\GroupBox.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\DebugView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Helpers\\DebugView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\PrincipledMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderHandle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SliderHandle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\ColorMasterSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\progress-indeterminate.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\progress-indeterminate.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolTip.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\ToolTip.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Dial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\SCurveTonemapSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\GroupBoxSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\GroupBoxSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\label-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassMaterial.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\FrostedGlassMaterial.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BlurSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\qmlmodels\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\FrustumCameraSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DistortionSphereSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\camera16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\HorizontalHeaderView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Material\\HorizontalHeaderView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\ShaderSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\scene16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\brushnoise.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\maps\\brushnoise.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\FrostedGlassSinglePassMaterialSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\Qt\\labs\\animation\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SplitView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\SplitView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\DesaturateSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Tumbler.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Tumbler.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab_selected.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\tab_selected.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\cylinder.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BusyIndicatorStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\designer\\PaperOfficeMaterialSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtWebEngine\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtWebEngine\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\CullModeSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\ToolButtonStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\itemdelegate-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Switch.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\Switch.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\toolbutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\SpinBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQml\\Models.2\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQml\\Models.2\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ProgressBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ProgressBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\ProgressBar.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\shaderutil16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ScrollViewHelper.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_medium.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\spinner_medium.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\GaussianDirectionalBlur.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolSeparatorSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\ToolSeparatorSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\BrushStrokesSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RangeSlider.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\RangeSlider.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RoundButtonSpecifics.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\RoundButtonSpecifics.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Materials\\maps\\concentric_milled_steel.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qmldir',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\qmldir',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtGraphicalEffects\\private\\DropShadowBase.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.jsc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\StackView.jsc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Desktop\\TreeViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ToolMenuButton.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Imagine\\ToolBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\groupbox.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\groupbox.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\BasicTableViewStyle.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\busyindicator-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ButtonGroup.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\ButtonGroup.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\PrivateWidgets\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\knob.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\knob.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\delaybutton-icon.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DelayButton.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\DelayButton.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\StatusBarStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\leftanglearrow.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\images\\leftanglearrow.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\group16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\ModalPopupBehavior.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Private\\BasicTableView.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\page-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\MenuBar.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\InsetSection.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\InsetSection.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Styles\\Base\\GroupBoxStyle.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\Fusion\\ToolSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon16.png',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\designer\\images\\textarea-icon16.png',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\plugins.qmltypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\LocalStorage\\plugins.qmltypes',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\<EMAIL>',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick3D\\Effects\\designer\\images\\<EMAIL>',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuSeparator.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls.2\\MenuSeparator.qml',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qmlc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Tab.qmlc',
   'DATA'),
  ('PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qml',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\qml\\QtQuick\\Controls\\Button.qml',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\hi.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\hi.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\lt.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\lt.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\zh-CN.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\zh-CN.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\nb.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\nb.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\et.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\et.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ko.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ko.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\pl.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\pl.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\he.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\he.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\tr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\tr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\qtwebengine_resources.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\resources\\qtwebengine_resources.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\en-GB.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\en-GB.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\pt-BR.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\pt-BR.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\pt-PT.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\pt-PT.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\el.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\el.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\bg.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\bg.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\en-US.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\en-US.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ja.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ja.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\es-419.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\es-419.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\it.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\it.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\gu.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\gu.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sw.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sw.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\zh-TW.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\zh-TW.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\kn.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\kn.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\fil.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\fil.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ca.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ca.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\icudtl.dat',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\resources\\icudtl.dat',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\lv.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\lv.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\id.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\id.pak',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\qt.conf',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\qt.conf',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\da.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\da.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\es.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\es.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\nl.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\nl.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ml.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ml.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ru.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ru.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sv.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sv.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ms.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ms.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\am.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\am.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\hu.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\hu.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\qtwebengine_devtools_resources.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\resources\\qtwebengine_devtools_resources.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\th.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\th.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ar.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ar.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\de.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\de.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\bn.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\bn.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ro.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ro.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\vi.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\vi.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\hr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\hr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\qtwebengine_resources_100p.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\resources\\qtwebengine_resources_100p.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\ta.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\ta.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\te.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\te.pak',
   'DATA'),
  ('PyQt5\\Qt5\\resources\\qtwebengine_resources_200p.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\resources\\qtwebengine_resources_200p.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\fr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\fr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sk.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sk.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\mr.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\mr.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\uk.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\uk.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\fa.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\fa.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\fi.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\fi.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\cs.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\cs.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtwebengine_locales\\sl.pak',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtwebengine_locales\\sl.pak',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtquickcontrols_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtquickcontrols_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtlocation_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtlocation_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('cv2\\__init__.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\base_library.zip',
   'DATA')],
 'python310.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
