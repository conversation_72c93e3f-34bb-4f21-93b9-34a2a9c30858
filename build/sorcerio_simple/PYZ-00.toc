('C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\build\\sorcerio_simple\\PYZ-00.pyz',
 [('PyQt5',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('appdirs',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\appdirs.py',
   'PYMODULE'),
  ('apscheduler',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\__init__.py',
   'PYMODULE'),
  ('apscheduler.events',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\events.py',
   'PYMODULE'),
  ('apscheduler.executors',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\executors\\__init__.py',
   'PYMODULE'),
  ('apscheduler.executors.asyncio',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\executors\\asyncio.py',
   'PYMODULE'),
  ('apscheduler.executors.base',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\executors\\base.py',
   'PYMODULE'),
  ('apscheduler.executors.debug',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\executors\\debug.py',
   'PYMODULE'),
  ('apscheduler.executors.gevent',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\executors\\gevent.py',
   'PYMODULE'),
  ('apscheduler.executors.pool',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\executors\\pool.py',
   'PYMODULE'),
  ('apscheduler.executors.tornado',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\executors\\tornado.py',
   'PYMODULE'),
  ('apscheduler.executors.twisted',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\executors\\twisted.py',
   'PYMODULE'),
  ('apscheduler.job',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\job.py',
   'PYMODULE'),
  ('apscheduler.jobstores',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\__init__.py',
   'PYMODULE'),
  ('apscheduler.jobstores.base',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\base.py',
   'PYMODULE'),
  ('apscheduler.jobstores.etcd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\etcd.py',
   'PYMODULE'),
  ('apscheduler.jobstores.memory',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\memory.py',
   'PYMODULE'),
  ('apscheduler.jobstores.mongodb',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\mongodb.py',
   'PYMODULE'),
  ('apscheduler.jobstores.redis',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\redis.py',
   'PYMODULE'),
  ('apscheduler.jobstores.rethinkdb',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\rethinkdb.py',
   'PYMODULE'),
  ('apscheduler.jobstores.sqlalchemy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\sqlalchemy.py',
   'PYMODULE'),
  ('apscheduler.jobstores.zookeeper',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\jobstores\\zookeeper.py',
   'PYMODULE'),
  ('apscheduler.schedulers',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\__init__.py',
   'PYMODULE'),
  ('apscheduler.schedulers.asyncio',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\asyncio.py',
   'PYMODULE'),
  ('apscheduler.schedulers.background',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\background.py',
   'PYMODULE'),
  ('apscheduler.schedulers.base',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\base.py',
   'PYMODULE'),
  ('apscheduler.schedulers.blocking',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\blocking.py',
   'PYMODULE'),
  ('apscheduler.schedulers.gevent',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\gevent.py',
   'PYMODULE'),
  ('apscheduler.schedulers.qt',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\qt.py',
   'PYMODULE'),
  ('apscheduler.schedulers.tornado',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\tornado.py',
   'PYMODULE'),
  ('apscheduler.schedulers.twisted',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\schedulers\\twisted.py',
   'PYMODULE'),
  ('apscheduler.triggers',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\__init__.py',
   'PYMODULE'),
  ('apscheduler.triggers.base',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\base.py',
   'PYMODULE'),
  ('apscheduler.triggers.calendarinterval',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\calendarinterval.py',
   'PYMODULE'),
  ('apscheduler.triggers.combining',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\combining.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\cron\\__init__.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron.expressions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\cron\\expressions.py',
   'PYMODULE'),
  ('apscheduler.triggers.cron.fields',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\cron\\fields.py',
   'PYMODULE'),
  ('apscheduler.triggers.date',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\date.py',
   'PYMODULE'),
  ('apscheduler.triggers.interval',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\triggers\\interval.py',
   'PYMODULE'),
  ('apscheduler.util',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\apscheduler\\util.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dis.py',
   'PYMODULE'),
  ('dotenv',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('download',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\download.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\opcode.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pathlib.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pprint.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE'),
  ('selenium',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browser',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browser.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browsing_context',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browsing_context.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.common',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\common.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.log',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\log.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.network',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\network.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.account',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\account.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.dialog',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\dialog.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.client_config',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\client_config.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.fedcm',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\fedcm.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.locator_converter',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\locator_converter.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\remote\\websocket_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\support\\select.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\support\\ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\signal.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('stats',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\stats.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tailscale_manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\tailscale_manager.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('tzdata',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Africa',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Africa\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Argentina',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Indiana',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.Kentucky',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.America.North_Dakota',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Antarctica',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Antarctica\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Arctic',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Arctic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Asia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Asia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Atlantic',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Atlantic\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Australia',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Australia\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Brazil',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Brazil\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Canada',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Canada\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Chile',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Chile\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Etc',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Etc\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Europe',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Europe\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Indian',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Indian\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Mexico',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Mexico\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.Pacific',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\Pacific\\__init__.py',
   'PYMODULE'),
  ('tzdata.zoneinfo.US',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzdata\\zoneinfo\\US\\__init__.py',
   'PYMODULE'),
  ('tzlocal',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal\\__init__.py',
   'PYMODULE'),
  ('tzlocal.unix',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal\\unix.py',
   'PYMODULE'),
  ('tzlocal.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal\\utils.py',
   'PYMODULE'),
  ('tzlocal.win32',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal\\win32.py',
   'PYMODULE'),
  ('tzlocal.windows_tz',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\tzlocal\\windows_tz.py',
   'PYMODULE'),
  ('ui',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\ui.py',
   'PYMODULE'),
  ('undetected_chromedriver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\undetected_chromedriver\\__init__.py',
   'PYMODULE'),
  ('undetected_chromedriver.cdp',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\undetected_chromedriver\\cdp.py',
   'PYMODULE'),
  ('undetected_chromedriver.dprocess',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\undetected_chromedriver\\dprocess.py',
   'PYMODULE'),
  ('undetected_chromedriver.options',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\undetected_chromedriver\\options.py',
   'PYMODULE'),
  ('undetected_chromedriver.patcher',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\undetected_chromedriver\\patcher.py',
   'PYMODULE'),
  ('undetected_chromedriver.reactor',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\undetected_chromedriver\\reactor.py',
   'PYMODULE'),
  ('undetected_chromedriver.webelement',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\undetected_chromedriver\\webelement.py',
   'PYMODULE'),
  ('upload',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\upload.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\utils.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uuid.py',
   'PYMODULE'),
  ('webdriver_manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('websocket',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websockets',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.auth',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.cli',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.client',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.extensions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.frames',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.headers',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.http',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.http11',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.imports',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.legacy',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.server',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.streams',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.sync',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.uri',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.utils',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.version',
   'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
