@echo off
title SorcerioModules - Professional Windows Installer Builder
echo ================================================================
echo SorcerioModules - Professional Windows Installer Builder
echo Company: Ozmorph
echo Application: Sorcerio
echo ================================================================
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python found. Starting installer build process...
echo.

REM Install required build dependencies
echo Installing build dependencies...
python -m pip install --upgrade pip
python -m pip install pyinstaller requests

echo.
echo ================================================================
echo Starting Professional Installer Build Process...
echo ================================================================
echo.

REM Run the PyInstaller-based builder
python pyinstaller_builder.py

if errorlevel 1 (
    echo.
    echo ================================================================
    echo BUILD FAILED!
    echo ================================================================
    echo Please check the error messages above.
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo Your professional Windows installer has been created in:
echo C:\Users\<USER>\Desktop\sorcsetup\
echo.
echo The installer is ready for:
echo - Commercial distribution
echo - Website upload and sales
echo - Customer deployment
echo.
echo Features included:
echo - Complete standalone installation
echo - Professional setup wizard
echo - License agreement
echo - Desktop and Start Menu shortcuts
echo - Automatic uninstaller
echo - Registry integration
echo - Modern Windows standards compliance
echo.
echo ================================================================
echo Ready for commercial distribution!
echo ================================================================
echo.
pause
