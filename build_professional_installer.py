#!/usr/bin/env python3
"""
Professional Installer Builder for Sorcerio by Ozmorph
This script creates a complete standalone Windows installer
"""

import os
import sys
import subprocess
import shutil
import logging
from pathlib import Path
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('installer_build.log'),
        logging.StreamHandler()
    ]
)

class SorcerioInstallerBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.output_dir = Path("C:/Users/<USER>/Desktop/sorcsetup")
        self.spec_file = self.project_root / "sorcerio_simple.spec"
        self.inno_script = self.project_root / "sorcerio_installer.iss"
        
    def clean_previous_builds(self):
        """Clean up previous build artifacts"""
        logging.info("Cleaning previous build artifacts...")

        try:
            # Remove dist and build directories
            if self.dist_dir.exists():
                shutil.rmtree(self.dist_dir)
                logging.info("Removed dist directory")

            if self.build_dir.exists():
                shutil.rmtree(self.build_dir)
                logging.info("Removed build directory")

            # Clean up any existing executables
            for exe_file in self.project_root.glob("*.exe"):
                try:
                    exe_file.unlink()
                    logging.info(f"Removed {exe_file}")
                except Exception as e:
                    logging.warning(f"Could not remove {exe_file}: {e}")

            logging.info("Cleanup completed successfully")
            return True

        except Exception as e:
            logging.error(f"Error during cleanup: {e}")
            return False
    
    def verify_dependencies(self):
        """Verify all required files and dependencies exist"""
        logging.info("Verifying dependencies...")
        
        required_files = [
            "main.py",
            "ui.py", 
            "utils.py",
            "download.py",
            "upload.py",
            "stats.py",
            "tailscale_manager.py",
            "requirements.txt",
            "instagram.ico",
            "x.ico"
        ]
        
        missing_files = []
        for file in required_files:
            if not (self.project_root / file).exists():
                missing_files.append(file)
        
        if missing_files:
            logging.error(f"Missing required files: {missing_files}")
            return False
            
        # Check for portable chromium
        chromium_dir = self.project_root / "portable_chromium"
        if not chromium_dir.exists():
            logging.error("Portable Chromium directory not found")
            return False
            
        logging.info("All dependencies verified successfully")
        return True
    
    def install_requirements(self):
        """Install all Python requirements"""
        logging.info("Installing Python requirements...")
        
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements_minimal.txt"
            ], check=True, cwd=self.project_root)
            
            # Install PyInstaller if not already installed
            subprocess.run([
                sys.executable, "-m", "pip", "install", "pyinstaller"
            ], check=True)
            
            logging.info("Requirements installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logging.error(f"Failed to install requirements: {e}")
            return False
    
    def build_executable(self):
        """Build the standalone executable using PyInstaller"""
        logging.info("Building standalone executable with PyInstaller...")
        
        try:
            # Run PyInstaller with our spec file
            cmd = [
                sys.executable, "-m", "PyInstaller",
                "--clean",
                "--noconfirm", 
                str(self.spec_file)
            ]
            
            logging.info(f"Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode != 0:
                logging.error(f"PyInstaller failed with return code {result.returncode}")
                logging.error(f"STDOUT: {result.stdout}")
                logging.error(f"STDERR: {result.stderr}")
                return False
            
            # Verify the executable was created
            exe_path = self.dist_dir / "Sorcerio" / "Sorcerio.exe"
            if not exe_path.exists():
                logging.error(f"Executable not found at {exe_path}")
                return False
                
            logging.info(f"Executable built successfully: {exe_path}")
            return True
            
        except Exception as e:
            logging.error(f"Error building executable: {e}")
            return False
    
    def verify_executable(self):
        """Verify the built executable works"""
        logging.info("Verifying executable...")
        
        exe_path = self.dist_dir / "Sorcerio" / "Sorcerio.exe"
        if not exe_path.exists():
            logging.error("Executable not found for verification")
            return False
            
        # Check file size (should be substantial)
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        logging.info(f"Executable size: {file_size:.1f} MB")
        
        if file_size < 50:  # Less than 50MB seems too small
            logging.warning("Executable seems unusually small")
            
        # Try to run it briefly (this might not work in all environments)
        try:
            # Just check if it starts without immediate crash
            process = subprocess.Popen([str(exe_path)], cwd=exe_path.parent)
            time.sleep(3)  # Let it start
            process.terminate()
            process.wait(timeout=5)
            logging.info("Executable verification passed")
            return True
        except Exception as e:
            logging.warning(f"Could not fully verify executable: {e}")
            # Don't fail the build for this
            return True
    
    def create_installer(self):
        """Create the Windows installer using Inno Setup"""
        logging.info("Creating Windows installer...")
        
        # Check if Inno Setup is installed
        inno_paths = [
            r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
            r"C:\Program Files\Inno Setup 6\ISCC.exe",
            r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
            r"C:\Program Files\Inno Setup 5\ISCC.exe"
        ]
        
        inno_exe = None
        for path in inno_paths:
            if os.path.exists(path):
                inno_exe = path
                break
        
        if not inno_exe:
            logging.error("Inno Setup not found. Please install Inno Setup from https://jrsoftware.org/isinfo.php")
            logging.info("Alternative: The executable is ready in dist/Sorcerio/ - you can distribute this folder")
            return False
        
        try:
            # Run Inno Setup compiler
            cmd = [inno_exe, str(self.inno_script)]
            logging.info(f"Running: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            
            if result.returncode != 0:
                logging.error(f"Inno Setup failed with return code {result.returncode}")
                logging.error(f"STDOUT: {result.stdout}")
                logging.error(f"STDERR: {result.stderr}")
                return False
            
            # Check if installer was created
            installer_path = self.output_dir / "SorcerioSetup_v1.0.exe"
            if installer_path.exists():
                installer_size = installer_path.stat().st_size / (1024 * 1024)  # MB
                logging.info(f"Installer created successfully: {installer_path}")
                logging.info(f"Installer size: {installer_size:.1f} MB")
                return True
            else:
                logging.error("Installer file not found after build")
                return False
                
        except Exception as e:
            logging.error(f"Error creating installer: {e}")
            return False
    
    def build_complete_installer(self):
        """Build the complete professional installer"""
        logging.info("=" * 60)
        logging.info("Starting Sorcerio Professional Installer Build")
        logging.info("=" * 60)
        
        steps = [
            ("Cleaning previous builds", self.clean_previous_builds),
            ("Verifying dependencies", self.verify_dependencies),
            ("Installing requirements", self.install_requirements),
            ("Building executable", self.build_executable),
            ("Verifying executable", self.verify_executable),
            ("Creating installer", self.create_installer)
        ]
        
        for step_name, step_func in steps:
            logging.info(f"\n--- {step_name} ---")
            if not step_func():
                logging.error(f"Build failed at step: {step_name}")
                return False
        
        logging.info("\n" + "=" * 60)
        logging.info("BUILD COMPLETED SUCCESSFULLY!")
        logging.info("=" * 60)
        logging.info(f"Installer location: {self.output_dir / 'SorcerioSetup_v1.0.exe'}")
        logging.info(f"Executable location: {self.dist_dir / 'Sorcerio'}")
        logging.info("The installer is ready for distribution!")
        
        return True

def main():
    """Main entry point"""
    builder = SorcerioInstallerBuilder()
    success = builder.build_complete_installer()
    
    if success:
        print("\n🎉 Professional installer build completed successfully!")
        print(f"📦 Installer: C:\\Users\\<USER>\\Desktop\\sorcsetup\\SorcerioSetup_v1.0.exe")
        print("🚀 Ready for distribution!")
    else:
        print("\n❌ Build failed. Check the logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
