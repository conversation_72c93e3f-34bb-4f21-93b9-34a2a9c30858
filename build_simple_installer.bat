@echo off
title SorcerioModules - Simple Professional Installer Builder
echo ================================================================
echo SorcerioModules - Simple Professional Installer Builder
echo Company: Ozmorph
echo Application: Sorcerio
echo ================================================================
echo.

REM Change to the script directory
cd /d "%~dp0"

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python found. Starting simple installer build process...
echo.

REM Install required build dependencies
echo Installing build dependencies...
python -m pip install --upgrade pip
python -m pip install pyinstaller requests

echo.
echo ================================================================
echo Starting Simple Professional Installer Build Process...
echo ================================================================
echo.

REM Run the simple installer builder
python simple_installer_builder.py

if errorlevel 1 (
    echo.
    echo ================================================================
    echo BUILD FAILED!
    echo ================================================================
    echo Please check the error messages above.
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo Your professional installer has been created in:
echo C:\Users\<USER>\Desktop\sorcsetup\
echo.
echo INSTALLATION INSTRUCTIONS:
echo 1. Extract the ZIP file
echo 2. Run "install.bat" as Administrator
echo 3. Follow the installation wizard
echo 4. Launch Sorcerio from desktop or start menu
echo.
echo The installer includes:
echo - Complete application with all dependencies
echo - Professional installation script
echo - Desktop and Start Menu shortcuts
echo - Automatic Python requirements installation
echo - Professional branding and documentation
echo.
echo ================================================================
echo Ready for commercial distribution!
echo ================================================================
echo.
pause
