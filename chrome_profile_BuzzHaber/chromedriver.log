[**********.210][INFO]: Starting ChromeDriver 113.0.5620.0 (f5cdb01c3f419b5600345dc2d0a48fc8534dc469-refs/heads/main@{#1110125}) on port 59708
[**********.210][INFO]: Please see https://chromedriver.chromium.org/security-considerations for suggestions on keeping ChromeDriver safe.
[**********.710][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND InitSession {
   "capabilities": {
      "alwaysMatch": {
         "browserName": "chrome",
         "goog:chromeOptions": {
            "args": [ "--headless=new", "--no-sandbox", "--disable-dev-shm-usage", "--disable-gpu", "--disable-software-rasterizer", "--disable-background-timer-throttling", "--disable-backgrounding-occluded-windows", "--disable-renderer-backgrounding", "--disable-features=TranslateUI,VizDisplayCompositor", "--disable-ipc-flooding-protection", "--disable-hang-monitor", "--disable-client-side-phishing-detection", "--disable-popup-blocking", "--disable-prompt-on-repost", "--disable-sync", "--disable-extensions", "--disable-plugins", "--disable-images", "--disable-notifications", "--disable-web-security", "--disable-blink-features=AutomationControlled", "--disable-logging", "--disable-default-apps", "--disable-background-networking", "--max_old_space_size=4096", "--memory-pressure-off", "--window-size=1920,1080", "--lang=en-US", "--force-color-profile=srgb", "--metrics-recording-only", "--use-mock-keychain", "--user-data-dir=C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\chrome_profile_BuzzHaber", "--remote-debugging-port=0", "--disable-crash-reporter", "--disable-logging", "--log-level=3", "--silent", "--disable-breakpad", "--disable-component-update", "--disable-domain-reliability", "--disable-field-trial-config" ],
            "binary": "C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\portable_chromium\\browser\\chrome-win\\chrome.exe",
            "excludeSwitches": [ "enable-automation", "enable-logging" ],
            "extensions": [  ],
            "prefs": {
               "profile.default_content_setting_values.geolocation": 2,
               "profile.default_content_setting_values.media_stream": 2,
               "profile.default_content_setting_values.notifications": 2,
               "profile.default_content_settings.popups": 0,
               "profile.managed_default_content_settings.images": 2,
               "profile.managed_default_content_settings.media_stream": 2
            },
            "useAutomationExtension": false
         },
         "pageLoadStrategy": "normal"
      },
      "firstMatch": [ {
      } ]
   }
}
[**********.711][WARNING]: Deprecated chrome option is ignored: useAutomationExtension
[**********.711][WARNING]: Deprecated chrome option is ignored: useAutomationExtension
[**********.712][INFO]: Populating Preferences file: {
   "account_tracker_service_last_update": "*****************",
   "alternate_error_pages": {
      "backup": false
   },
   "autocomplete": {
      "retention_policy_last_version": 113
   },
   "autofill": {
      "orphan_rows_removed": true
   },
   "browser": {
      "check_default_browser": false,
      "window_placement": {
         "bottom": 1090,
         "left": 10,
         "maximized": false,
         "right": 1930,
         "top": 10,
         "work_area_bottom": 728,
         "work_area_left": 0,
         "work_area_right": 1366,
         "work_area_top": 0
      }
   },
   "commerce_daily_metrics_last_update_time": "*****************",
   "countryid_at_install": 21586,
   "dips_timer_last_update": "*****************",
   "distribution": {
      "import_bookmarks": false,
      "import_history": false,
      "import_search_engine": false,
      "make_chrome_default_for_user": false,
      "skip_first_run_ui": true
   },
   "dns_prefetching": {
      "enabled": false
   },
   "domain_diversity": {
      "last_reporting_timestamp": "*****************"
   },
   "extensions": {
      "alerts": {
         "initialized": true
      },
      "chrome_url_overrides": {
      }
   },
   "gaia_cookie": {
      "changed_time": **********.547809,
      "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=",
      "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"
   },
   "gcm": {
      "product_category_for_subtypes": "org.chromium.windows"
   },
   "google": {
      "services": {
         "consented_to_sync": false,
         "signin_scoped_device_id": "763e0e30-f98f-4503-90b5-73d5bb91d9f1"
      }
   },
   "intl": {
      "selected_languages": "en-US,en"
   },
   "invalidation": {
      "per_sender_topics_to_handler": {
         "*************": {
         },
         "**********": {
         }
      }
   },
   "media": {
      "device_id_salt": "44EF6D41E407F6663EDE4F48FAD1A6D5",
      "engagement": {
         "schema_version": 5
      }
   },
   "media_router": {
      "receiver_id_hash_token": "USPyoHEYT2Ob9ztk+fnrnpHlWTerbfjwhRd3/4+SBGOf8LczJniFExgK/ghSHFUxWkvt29CS8RVNOEV5rVmRJA=="
   },
   "ntp": {
      "num_personal_suggestions": 3
   },
   "optimization_guide": {
      "hintsfetcher": {
         "hosts_successfully_fetched": {
         }
      },
      "predictionmodelfetcher": {
         "last_fetch_attempt": "13393601032912951"
      },
      "previously_registered_optimization_types": {
         "ABOUT_THIS_SITE": true,
         "HISTORY_CLUSTERS": true
      },
      "store_file_paths_to_delete": {
      }
   },
   "privacy_sandbox": {
      "anti_abuse_initialized": true
   },
   "profile": {
      "avatar_index": 26,
      "content_settings": {
         "enable_quiet_permission_ui_enabling_method": {
            "notifications": 1
         },
         "exceptions": {
            "accessibility_events": {
            },
            "anti_abuse": {
            },
            "app_banner": {
               "https://x.com:443,*": {
                  "last_modified": "13393562540473081",
                  "setting": {
                     "https://x.com/": {
                        "next_install_text_animation": {
                           "delay": "86400000000",
                           "last_shown": "13393562540472462"
                        }
                     },
                     "https://x.com/?utm_source=homescreen&utm_medium=shortcut": {
                        "couldShowBannerEvents": 1.3393562540473062e+16
                     }
                  }
               }
            },
            "ar": {
            },
            "auto_select_certificate": {
            },
            "automatic_downloads": {
            },
            "autoplay": {
            },
            "background_sync": {
            },
            "bluetooth_chooser_data": {
            },
            "bluetooth_guard": {
            },
            "bluetooth_scanning": {
            },
            "camera_pan_tilt_zoom": {
            },
            "client_hints": {
            },
            "clipboard": {
            },
            "cookies": {
            },
            "durable_storage": {
            },
            "fedcm_active_session": {
            },
            "fedcm_idp_registration": {
            },
            "fedcm_idp_signin": {
               "https://accounts.google.com:443,*": {
                  "last_modified": "*****************",
                  "setting": {
                     "chosen-objects": [ {
                        "idp-origin": "https://accounts.google.com",
                        "idp-signin-status": false
                     } ]
                  }
               }
            },
            "fedcm_share": {
            },
            "file_system_access_chooser_data": {
            },
            "file_system_last_picked_directory": {
            },
            "file_system_read_guard": {
            },
            "file_system_write_guard": {
            },
            "formfill_metadata": {
               "https://x.com:443,*": {
                  "last_modified": "*****************",
                  "setting": {
                     "UserDataFieldFilled": true
                  }
               }
            },
            "geolocation": {
            },
            "get_display_media_set_select_all_screens": {
            },
            "hid_chooser_data": {
            },
            "hid_guard": {
            },
            "http_allowed": {
            },
            "idle_detection": {
            },
            "images": {
            },
            "important_site_info": {
            },
            "insecure_private_network": {
            },
            "intent_picker_auto_display": {
            },
            "javascript": {
            },
            "javascript_jit": {
            },
            "legacy_cookie_access": {
            },
            "local_fonts": {
            },
            "media_engagement": {
               "https://x.com:443,*": {
                  "expiration": "13401376861043262",
                  "last_modified": "13393600861043272",
                  "setting": {
                     "hasHighScore": false,
                     "lastMediaPlaybackTime": 0.0,
                     "mediaPlaybacks": 0,
                     "visits": 5
                  }
               }
            },
            "media_stream_camera": {
            },
            "media_stream_mic": {
            },
            "midi_sysex": {
            },
            "mixed_script": {
            },
            "nfc_devices": {
            },
            "notification_interactions": {
            },
            "notification_permission_review": {
            },
            "notifications": {
            },
            "password_protection": {
            },
            "payment_handler": {
            },
            "permission_autoblocking_data": {
            },
            "permission_autorevocation_data": {
            },
            "popups": {
            },
            "private_network_chooser_data": {
            },
            "private_network_guard": {
            },
            "protected_media_identifier": {
            },
            "protocol_handler": {
            },
            "reduced_accept_language": {
            },
            "safe_browsing_url_check_data": {
            },
            "sensors": {
            },
            "serial_chooser_data": {
            },
            "serial_guard": {
            },
            "site_engagement": {
               "https://x.com:443,*": {
                  "last_modified": "13393601030904336",
                  "setting": {
                     "lastEngagementTime": 1.3393601030904308e+16,
                     "lastShortcutLaunchTime": 0.0,
                     "pointsAddedToday": 15.0,
                     "rawScore": 14.6315584512
                  }
               }
            },
            "sound": {
            },
            "ssl_cert_decisions": {
            },
            "storage_access": {
            },
            "subresource_filter": {
            },
            "subresource_filter_data": {
            },
            "top_level_storage_access": {
            },
            "unused_site_permissions": {
            },
            "usb_chooser_data": {
            },
            "usb_guard": {
            },
            "vr": {
            },
            "webid_api": {
            },
            "webid_auto_reauthn": {
            },
            "window_placement": {
            }
         },
         "pattern_pairs": {
            "https://*,*": {
               "media-stream": {
                  "audio": "Default",
                  "video": "Default"
               }
            }
         },
         "pref_version": 1
      },
      "creation_time": "13393562300827066",
      "default_content_setting_values": {
         "geolocation": 2,
         "media_stream": 2,
         "notifications": 2
      },
      "default_content_settings": {
         "geolocation": 1,
         "mouselock": 1,
         "notifications": 1,
         "popups": 0,
         "ppapi-broker": 1
      },
      "exit_type": "Crashed",
      "last_engagement_time": "13393601030904307",
      "last_time_obsolete_http_credentials_removed": 1749088957.103163,
      "last_time_password_store_metrics_reported": 1749088927.101718,
      "managed_default_content_settings": {
         "images": 2,
         "media_stream": 2
      },
      "managed_user_id": "",
      "name": "Person 1",
      "password_manager_enabled": false,
      "were_old_google_logins_removed": true
   },
   "safebrowsing": {
      "enabled": false,
      "event_timestamps": {
      },
      "metrics_last_log_time": "13393562301"
   },
   "search": {
      "suggest_enabled": false
   },
   "segmentation_platform": {
      "device_switcher_util": {
         "result": {
            "labels": [ "NotSynced" ]
         }
      },
      "last_db_compaction_time": "13393468799000000"
   },
   "sessions": {
      "event_log": [ {
         "crashed": false,
         "time": "13393562301115567",
         "type": 0
      }, {
         "did_schedule_command": true,
         "first_session_service": true,
         "tab_count": 1,
         "time": "13393562329379843",
         "type": 2,
         "window_count": 1
      }, {
         "crashed": false,
         "time": "13393562497045376",
         "type": 0
      }, {
         "crashed": true,
         "time": "13393562742112420",
         "type": 0
      }, {
         "did_schedule_command": false,
         "first_session_service": true,
         "tab_count": 1,
         "time": "13393562767001208",
         "type": 2,
         "window_count": 1
      }, {
         "crashed": true,
         "time": "13393591161635261",
         "type": 0
      }, {
         "did_schedule_command": false,
         "first_session_service": true,
         "tab_count": 1,
         "time": "13393591186660271",
         "type": 2,
         "window_count": 1
      }, {
         "crashed": true,
         "time": "13393593835389697",
         "type": 0
      }, {
         "did_schedule_command": false,
         "first_session_service": true,
         "tab_count": 1,
         "time": "13393593860408084",
         "type": 2,
         "window_count": 1
      }, {
         "crashed": true,
         "time": "13393600835777357",
         "type": 0
      }, {
         "did_schedule_command": false,
         "first_session_service": true,
         "tab_count": 1,
         "time": "13393600861036361",
         "type": 2,
         "window_count": 1
      }, {
         "crashed": true,
         "time": "13393601017229153",
         "type": 0
      } ],
      "session_data_status": 1
   },
   "settings": {
      "a11y": {
         "apply_page_colors_only_on_increased_contrast": true
      }
   },
   "signin": {
      "allowed": false
   },
   "spellcheck": {
      "dictionaries": [ "en-US" ],
      "dictionary": ""
   },
   "translate": {
      "enabled": false
   },
   "translate_site_blacklist": [  ],
   "translate_site_blocklist_with_time": {
   },
   "web_apps": {
      "daily_metrics": {
         "https://x.com/?utm_source=homescreen&utm_medium=shortcut": {
            "background_duration_sec": 0,
            "effective_display_mode": 3,
            "foreground_duration_sec": 0,
            "installed": false,
            "num_sessions": 0,
            "promotable": true
         }
      },
      "daily_metrics_date": "13393544400000000",
      "did_migrate_default_chrome_apps": [ "MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite" ],
      "last_preinstall_synchronize_version": "113"
   }
}
[**********.714][INFO]: Populating Local State file: {
   "background_mode": {
      "enabled": false
   },
   "browser": {
      "shortcut_migration_version": "113.0.5620.0"
   },
   "hardware_acceleration_mode_previous": true,
   "invalidation": {
      "per_sender_topics_to_handler": {
      }
   },
   "legacy": {
      "profile": {
         "name": {
            "migrated": true
         }
      }
   },
   "management": {
      "platform": {
         "azure_active_directory": 0,
         "enterprise_mdm_win": 0
      }
   },
   "os_crypt": {
      "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAAApzOkEXkSyTYZ6Bei7Oi8bAAAAAAIAAAAAABBmAAAAAQAAIAAAANScyvEC6z6BLBbBd0d7uPRjHEkRkTc1ugeADXRHyanBAAAAAA6AAAAAAgAAIAAAAAJiMw9y6pftK2NK1rYK7YXrMgWW0cX3Ve+TAP5gWk7nMAAAAHxCtB4ZKK5Q/pwjGanJCzPU91vqHu3++TUbxqJO0wcz24UcdOg8xiTAQLIZoIDfV0AAAACEg1ExW3NzzBsFv9766K2zppVo8L90Pn4WHkSxdo6xapXYcrvienPiYEhrz/nVe+suAFlgyegkbpH4jP3082GO"
   },
   "policy": {
      "last_statistics_update": "*****************"
   },
   "profile": {
      "info_cache": {
         "Default": {
            "active_time": **********.879193,
            "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_26",
            "background_apps": false,
            "force_signin_profile_locked": false,
            "gaia_given_name": "",
            "gaia_id": "",
            "gaia_name": "",
            "hosted_domain": "",
            "is_consented_primary_account": false,
            "is_ephemeral": false,
            "is_using_default_avatar": true,
            "is_using_default_name": true,
            "managed_user_id": "",
            "metrics_bucket_index": 1,
            "name": "Person 1",
            "signin.with_credential_provider": false,
            "user_name": ""
         }
      },
      "last_active_profiles": [ "Default" ],
      "metrics": {
         "next_bucket_index": 2
      },
      "profile_counts_reported": "*****************"
   },
   "profile_network_context_service": {
      "http_cache_finch_experiment_groups": "None None None None"
   },
   "session_id_generator_last_value": "**********",
   "shutdown": {
      "num_processes": 0,
      "num_processes_slow": 0,
      "type": 0
   },
   "ssl": {
      "rev_checking": {
         "enabled": false
      }
   },
   "subresource_filter": {
      "ruleset_version": {
         "checksum": 0,
         "content": "",
         "format": 0
      }
   },
   "tab_stats": {
      "discards_external": 0,
      "discards_proactive": 0,
      "discards_urgent": 0,
      "last_daily_sample": "13393562301200644",
      "max_tabs_per_window": 1,
      "reloads_external": 0,
      "reloads_proactive": 0,
      "reloads_urgent": 0,
      "total_tab_count_max": 1,
      "window_count_max": 1
   },
   "ukm": {
      "persisted_logs": [  ]
   },
   "uninstall_metrics": {
      "installation_date2": "**********"
   },
   "user_experience_metrics": {
      "client_id2": "9757f352-edff-405f-b7c3-944f99f7cfc1",
      "client_id_timestamp": "**********",
      "low_entropy_source3": 4094,
      "machine_id": 7946127,
      "pseudo_low_entropy_source": 3580,
      "session_id": 6,
      "stability": {
         "browser_last_live_timestamp": "13393601017070150",
         "exited_cleanly": true,
         "saved_system_profile": "CNDv/J4GEhUxMTMuMC41NjIwLjAtNjQtZGV2ZWwYkNiDwgYiBWVuLVVTKhgKCldpbmRvd3MgTlQSCjEwLjAuMTc3NjMydwoGeDg2XzY0EN3/AxiAgOCW2/8fIhRQcm9MaWFudCBETDM4MHAgR2VuOCgBMNYKOIAGQgoIABAAGgAyADoATQAAAABVAAAAAGUAAIA/ahYKDEdlbnVpbmVJbnRlbBDkjQwYKCAAggEAigEAqgEGeDg2XzY0sAEBSgoNbSM6XhXQhuJZSgoNwWCc7hXEjz+ASgoNkrdXsxXfF0o/UABqDAgAEAAYACAGOABAAIABkNiDwgaYAQD4Af4fgAL///////////8BiAIAkgIkOTc1N2YzNTItZWRmZi00MDVmLWI3YzMtOTQ0Zjk5ZjdjZmMxqAL8G7ICwAFkHjnSN+KtfsWIACp+yLBs25szqQuPvS/YwC2xRnQ0OvK351ruai1/Kn4sxT+X+9wZDNsJTLRUE0n9H/8QADyTVB+N8hH54D7KZX4bmBQbqBy8eWLb+1MuY3cefXlLqmkeDEaNhCsFa+dsNWLf76xnsdTFt/kMNvS1EIfSN0RE6novhUnaUwbnPshwCq6RfojWxgqmOQvdoidEg0ktvAubXw4LwGPrbd3oElc2syKFv6tioimEi7j/TPMNXXu8DQM=",
         "saved_system_profile_hash": "5C7378756AC12A3B1B6ABA4FFBD19030F13B9C85",
         "stats_buildtime": "1675573200",
         "stats_version": "113.0.5620.0-64-devel",
         "system_crash_count": 0
      }
   },
   "variations_crash_streak": 1,
   "was": {
      "restarted": false
   }
}
[**********.718][INFO]: Launching chrome: "C:\Users\<USER>\PycharmProjects\SorcerioModules\portable_chromium\browser\chrome-win\chrome.exe" --allow-pre-commit-input --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-blink-features=AutomationControlled --disable-breakpad --disable-client-side-phishing-detection --disable-component-update --disable-crash-reporter --disable-default-apps --disable-dev-shm-usage --disable-domain-reliability --disable-extensions --disable-features=TranslateUI,VizDisplayCompositor --disable-field-trial-config --disable-gpu --disable-hang-monitor --disable-images --disable-ipc-flooding-protection --disable-logging --disable-notifications --disable-plugins --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --disable-software-rasterizer --disable-sync --disable-web-security --enable-blink-features=ShadowDOMV0 --force-color-profile=srgb --headless=new --lang=en-US --log-level=3 --max_old_space_size=4096 --memory-pressure-off --metrics-recording-only --no-first-run --no-sandbox --no-service-autorun --password-store=basic --remote-debugging-port=0 --silent --test-type=webdriver --use-mock-keychain --user-data-dir="C:\Users\<USER>\PycharmProjects\SorcerioModules\chrome_profile_BuzzHaber" --window-size=1920,1080 data:,
[**********.089][DEBUG]: DevTools HTTP Request: http://localhost:59711/json/version
[**********.398][DEBUG]: DevTools HTTP Response: {

   "Browser": "Chrome/113.0.5620.0",

   "Protocol-Version": "1.3",

   "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",

   "V8-Version": "11.3.9",

   "WebKit-Version": "537.36 (@f5cdb01c3f419b5600345dc2d0a48fc8534dc469)",

   "webSocketDebuggerUrl": "ws://localhost:59711/devtools/browser/5cdd61df-32e0-4749-b3df-d2467ac4a7f6"

}


[**********.398][DEBUG]: DevTools HTTP Request: http://localhost:59711/json/list
[**********.400][DEBUG]: DevTools HTTP Response: [ {

   "description": "",

   "devtoolsFrontendUrl": "/devtools/inspector.html?ws=localhost:59711/devtools/page/7C06568396BD027AF2A2803C59BE8E07",

   "id": "7C06568396BD027AF2A2803C59BE8E07",

   "title": "data:,",

   "type": "page",

   "url": "data:,",

   "webSocketDebuggerUrl": "ws://localhost:59711/devtools/page/7C06568396BD027AF2A2803C59BE8E07"

} ]


[**********.405][INFO]: resolved localhost to ["::1","127.0.0.1"]
[**********.406][DEBUG]: DevTools WebSocket Command: Target.getTargets (id=1) (session_id=) browser {
}
[**********.407][DEBUG]: DevTools WebSocket Response: Target.getTargets (id=1) (session_id=) browser {
   "targetInfos": [ {
      "attached": false,
      "browserContextId": "49C24AE394506CB36C128C7A3AFE983C",
      "canAccessOpener": false,
      "targetId": "7C06568396BD027AF2A2803C59BE8E07",
      "title": "data:,",
      "type": "page",
      "url": "data:,"
   } ]
}
[**********.407][DEBUG]: DevTools WebSocket Command: Target.attachToTarget (id=2) (session_id=) browser {
   "flatten": true,
   "targetId": "7C06568396BD027AF2A2803C59BE8E07"
}
[**********.408][DEBUG]: DevTools WebSocket Event: Target.attachedToTarget (session_id=) browser {
   "sessionId": "0C7E126E74C468CEE17CF3D378841D60",
   "targetInfo": {
      "attached": true,
      "browserContextId": "49C24AE394506CB36C128C7A3AFE983C",
      "canAccessOpener": false,
      "targetId": "7C06568396BD027AF2A2803C59BE8E07",
      "title": "data:,",
      "type": "page",
      "url": "data:,"
   },
   "waitingForDebugger": false
}
[**********.408][DEBUG]: DevTools WebSocket Response: Target.attachToTarget (id=2) (session_id=) browser {
   "sessionId": "0C7E126E74C468CEE17CF3D378841D60"
}
[**********.409][DEBUG]: DevTools WebSocket Command: Page.addScriptToEvaluateOnNewDocument (id=3) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "source": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;}) ();"
}
[**********.409][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=4) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;}) ();"
}
[**********.409][DEBUG]: DevTools WebSocket Command: Log.enable (id=5) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.409][DEBUG]: DevTools WebSocket Command: Target.setAutoAttach (id=6) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "autoAttach": true,
   "flatten": true,
   "waitForDebuggerOnStart": false
}
[**********.409][DEBUG]: DevTools WebSocket Command: Page.enable (id=7) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.409][DEBUG]: DevTools WebSocket Command: Page.enable (id=8) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.409][DEBUG]: DevTools WebSocket Response: Page.addScriptToEvaluateOnNewDocument (id=3) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "identifier": "1"
}
[**********.410][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=4) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "undefined"
   }
}
[**********.410][DEBUG]: DevTools WebSocket Response: Log.enable (id=5) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.410][DEBUG]: DevTools WebSocket Response: Target.setAutoAttach (id=6) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.410][DEBUG]: DevTools WebSocket Response: Page.enable (id=7) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.410][DEBUG]: DevTools WebSocket Response: Page.enable (id=8) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.410][DEBUG]: DevTools WebSocket Command: Runtime.enable (id=9) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.411][DEBUG]: DevTools WebSocket Event: Runtime.executionContextCreated (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "context": {
      "auxData": {
         "frameId": "7C06568396BD027AF2A2803C59BE8E07",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "://",
      "uniqueId": "-1731602883426722029.8833014848920674504"
   }
}
[**********.411][DEBUG]: DevTools WebSocket Response: Runtime.enable (id=9) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.412][DEBUG]: DevTools WebSocket Command: Page.enable (id=10) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.412][DEBUG]: DevTools WebSocket Response: Page.enable (id=10) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.412][DEBUG]: DevTools WebSocket Command: Runtime.enable (id=11) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.413][DEBUG]: DevTools WebSocket Response: Runtime.enable (id=11) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.414][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=12) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.420][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=12) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": 1
      }
   }
}
[**********.420][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE InitSession {
   "capabilities": {
      "acceptInsecureCerts": false,
      "browserName": "chrome",
      "browserVersion": "113.0.5620.0",
      "chrome": {
         "chromedriverVersion": "113.0.5620.0 (f5cdb01c3f419b5600345dc2d0a48fc8534dc469-refs/heads/main@{#1110125})",
         "userDataDir": "C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\chrome_profile_BuzzHaber"
      },
      "goog:chromeOptions": {
         "debuggerAddress": "localhost:59711"
      },
      "networkConnectionEnabled": false,
      "pageLoadStrategy": "normal",
      "platformName": "windows",
      "proxy": {
      },
      "setWindowRect": true,
      "strictFileInteractability": false,
      "timeouts": {
         "implicit": 0,
         "pageLoad": 300000,
         "script": 30000
      },
      "unhandledPromptBehavior": "dismiss and notify",
      "webauthn:extension:credBlob": true,
      "webauthn:extension:largeBlob": true,
      "webauthn:extension:minPinLength": true,
      "webauthn:extension:prf": true,
      "webauthn:virtualAuthenticators": true
   },
   "sessionId": "98e435b56d0e8e201713f0d012b77a2b"
}
[**********.424][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND SetTimeouts {
   "pageLoad": 30000
}
[**********.424][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE SetTimeouts
[**********.426][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND SetTimeouts {
   "implicit": 10000
}
[**********.426][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE SetTimeouts
[**********.428][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND Navigate {
   "url": "data:text/html,\u003Chtml>\u003Cbody>Chrome Test\u003C/body>\u003C/html>"
}
[**********.428][INFO]: Waiting for pending navigations...
[**********.428][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=13) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.429][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=13) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.430][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=14) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "document",
   "objectGroup": "dfd3a7f3-1428-49de-8a0d-2ee42fe51945"
}
[**********.431][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=14) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "className": "HTMLDocument",
      "description": "#document",
      "objectId": "-3313525566676478686.1.1",
      "subtype": "node",
      "type": "object"
   }
}
[**********.431][DEBUG]: DevTools WebSocket Command: DOM.describeNode (id=15) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "objectId": "-3313525566676478686.1.1"
}
[**********.432][DEBUG]: DevTools WebSocket Response: DOM.describeNode (id=15) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "node": {
      "backendNodeId": 1,
      "baseURL": "data:,",
      "childNodeCount": 1,
      "compatibilityMode": "NoQuirksMode",
      "documentURL": "data:,",
      "localName": "",
      "nodeId": 0,
      "nodeName": "#document",
      "nodeType": 9,
      "nodeValue": "",
      "xmlVersion": ""
   }
}
[**********.432][DEBUG]: DevTools WebSocket Command: Runtime.releaseObjectGroup (id=16) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "objectGroup": "dfd3a7f3-1428-49de-8a0d-2ee42fe51945"
}
[**********.432][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=17) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true
}
[**********.433][DEBUG]: DevTools WebSocket Response: Runtime.releaseObjectGroup (id=16) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.433][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=17) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "complete"
   }
}
[**********.433][INFO]: Done waiting for pending navigations. Status: ok
[**********.433][DEBUG]: DevTools WebSocket Command: Page.navigate (id=18) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "url": "data:text/html,\u003Chtml>\u003Cbody>Chrome Test\u003C/body>\u003C/html>"
}
[**********.446][DEBUG]: DevTools WebSocket Response: Page.navigate (id=18) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07",
   "loaderId": "2EEE0381E2CEE8FCAA8472A36D9E847E"
}
[**********.447][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=19) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "document.URL"
}
[**********.450][DEBUG]: DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.451][DEBUG]: DevTools WebSocket Event: Page.frameStartedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07"
}
[**********.469][DEBUG]: DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.469][DEBUG]: DevTools WebSocket Event: Page.frameNavigated (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "",
      "gatedAPIFeatures": [  ],
      "id": "7C06568396BD027AF2A2803C59BE8E07",
      "loaderId": "2EEE0381E2CEE8FCAA8472A36D9E847E",
      "mimeType": "text/html",
      "secureContextType": "InsecureScheme",
      "securityOrigin": "://",
      "url": "data:text/html,\u003Chtml>\u003Cbody>Chrome Test\u003C/body>\u003C/html>"
   },
   "type": "Navigation"
}
[**********.469][DEBUG]: DevTools WebSocket Event: Runtime.executionContextCreated (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "context": {
      "auxData": {
         "frameId": "7C06568396BD027AF2A2803C59BE8E07",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "://",
      "uniqueId": "377671989318827733.-1979857826713453446"
   }
}
[**********.475][DEBUG]: DevTools WebSocket Event: Page.loadEventFired (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "timestamp": 44618.830528
}
[**********.475][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=20) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true
}
[**********.475][DEBUG]: DevTools WebSocket Event: Page.frameStoppedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07"
}
[**********.475][DEBUG]: DevTools WebSocket Event: Page.domContentEventFired (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "timestamp": 44618.837776
}
[**********.475][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=19) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "data:text/html,\u003Chtml>\u003Cbody>Chrome Test\u003C/body>\u003C/html>"
   }
}
[**********.476][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=20) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "complete"
   }
}
[**********.476][INFO]: Waiting for pending navigations...
[**********.476][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=21) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.477][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=21) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.477][INFO]: Done waiting for pending navigations. Status: ok
[**********.477][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE Navigate
[**********.479][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND GetSource {
}
[**********.479][INFO]: Waiting for pending navigations...
[**********.479][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=22) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.480][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=22) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.480][INFO]: Done waiting for pending navigations. Status: ok
[**********.481][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=23) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.486][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=23) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "\u003Chtml>\u003Chead>\u003C/head>\u003Cbody>Chrome Test\u003C/body>\u003C/html>"
      }
   }
}
[**********.486][INFO]: Waiting for pending navigations...
[**********.486][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=24) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.488][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=24) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.488][INFO]: Done waiting for pending navigations. Status: ok
[**********.488][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE GetSource "\u003Chtml>\u003Chead>\u003C/head>\u003Cbody>Chrome Test\u003C/body>\u003C/html>"
[**********.491][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND Navigate {
   "url": "https://x.com/i/flow/login"
}
[**********.491][INFO]: Waiting for pending navigations...
[**********.491][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=25) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.491][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=25) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.492][INFO]: Done waiting for pending navigations. Status: ok
[**********.492][DEBUG]: DevTools WebSocket Command: Page.navigate (id=26) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "url": "https://x.com/i/flow/login"
}
[**********.506][DEBUG]: DevTools WebSocket Event: Target.attachedToTarget (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "sessionId": "A1DC950642C299370FB2B0D9C9617B31",
   "targetInfo": {
      "attached": true,
      "browserContextId": "49C24AE394506CB36C128C7A3AFE983C",
      "canAccessOpener": false,
      "targetId": "7C0F7C10A91C00AB768C3143CDB4C8BC",
      "title": "Service Worker https://x.com/sw.js",
      "type": "service_worker",
      "url": "https://x.com/sw.js"
   },
   "waitingForDebugger": false
}
[**********.911][DEBUG]: DevTools WebSocket Response: Page.navigate (id=26) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07",
   "loaderId": "CAB0E407B3DFBC9EC97042E70655A9AF"
}
[**********.911][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=27) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "document.URL"
}
[**********.912][DEBUG]: DevTools WebSocket Event: Target.detachedFromTarget (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "sessionId": "A1DC950642C299370FB2B0D9C9617B31",
   "targetId": "7C0F7C10A91C00AB768C3143CDB4C8BC"
}
[**********.916][DEBUG]: DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.920][DEBUG]: DevTools WebSocket Event: Page.frameStartedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07"
}
[**********.934][DEBUG]: DevTools WebSocket Event: Target.attachedToTarget (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "sessionId": "4720D86B62FAF6117F06B5D303CD8B43",
   "targetInfo": {
      "attached": true,
      "browserContextId": "49C24AE394506CB36C128C7A3AFE983C",
      "canAccessOpener": false,
      "targetId": "7C0F7C10A91C00AB768C3143CDB4C8BC",
      "title": "Service Worker https://x.com/sw.js",
      "type": "service_worker",
      "url": "https://x.com/sw.js"
   },
   "waitingForDebugger": false
}
[**********.948][DEBUG]: DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.948][DEBUG]: DevTools WebSocket Event: Page.frameNavigated (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "x.com",
      "gatedAPIFeatures": [  ],
      "id": "7C06568396BD027AF2A2803C59BE8E07",
      "loaderId": "CAB0E407B3DFBC9EC97042E70655A9AF",
      "mimeType": "text/html",
      "secureContextType": "Secure",
      "securityOrigin": "https://x.com",
      "url": "https://x.com/i/flow/login"
   },
   "type": "Navigation"
}
[**********.948][DEBUG]: DevTools WebSocket Event: Runtime.executionContextCreated (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "context": {
      "auxData": {
         "frameId": "7C06568396BD027AF2A2803C59BE8E07",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "https://x.com",
      "uniqueId": "-6388471398369848870.-2679210903859494851"
   }
}
[**********.952][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=27) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "https://x.com/i/flow/login"
   }
}
[**********.952][INFO]: Waiting for pending navigations...
[**********.952][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=28) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.961][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=28) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.462][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=29) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.530][DEBUG]: DevTools WebSocket Event: Page.navigatedWithinDocument (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07",
   "url": "https://x.com/i/flow/login"
}
[**********.536][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=29) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.539][DEBUG]: DevTools WebSocket Event: Page.domContentEventFired (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "timestamp": 44619.905078
}
[**********.539][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=30) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.548][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=30) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.636][DEBUG]: DevTools WebSocket Event: Page.loadEventFired (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "timestamp": 44619.999154
}
[**********.636][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=31) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true
}
[**********.637][DEBUG]: DevTools WebSocket Event: Page.frameStoppedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07"
}
[**********.668][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=31) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "complete"
   }
}
[**********.668][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=32) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.671][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=32) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.671][INFO]: Done waiting for pending navigations. Status: ok
[**********.671][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE Navigate
[**********.854][DEBUG]: DevTools WebSocket Event: Page.frameAttached (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "97547D41B723EF1E233C79DB0FE10EAD",
   "parentFrameId": "7C06568396BD027AF2A2803C59BE8E07",
   "stack": {
      "callFrames": [ {
         "columnNumber": 81,
         "functionName": "lr",
         "lineNumber": 271,
         "scriptId": "213",
         "url": "https://accounts.google.com/gsi/client"
      }, {
         "columnNumber": 448,
         "functionName": "Kr",
         "lineNumber": 318,
         "scriptId": "213",
         "url": "https://accounts.google.com/gsi/client"
      }, {
         "columnNumber": 431,
         "functionName": "Lr",
         "lineNumber": 277,
         "scriptId": "213",
         "url": "https://accounts.google.com/gsi/client"
      }, {
         "columnNumber": 2449070,
         "functionName": "_renderGoogleSignInButton",
         "lineNumber": 0,
         "scriptId": "41",
         "url": "https://abs.twimg.com/responsive-web/client-web/main.025bbe0a.js"
      }, {
         "columnNumber": 2450144,
         "functionName": "",
         "lineNumber": 0,
         "scriptId": "41",
         "url": "https://abs.twimg.com/responsive-web/client-web/main.025bbe0a.js"
      }, {
         "columnNumber": 53213,
         "functionName": "Ua",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 100475,
         "functionName": "ki",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 100055,
         "functionName": "bi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 99959,
         "functionName": "bi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 99959,
         "functionName": "bi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 99959,
         "functionName": "bi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 99599,
         "functionName": "yi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 111262,
         "functionName": "",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 111764,
         "functionName": "ws",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 104324,
         "functionName": "ls",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 1363,
         "functionName": "x",
         "lineNumber": 27,
         "scriptId": "9",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-adcb47af.11578c3a.js"
      }, {
         "columnNumber": 1895,
         "functionName": "P",
         "lineNumber": 27,
         "scriptId": "9",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-adcb47af.11578c3a.js"
      } ]
   }
}
[**********.855][DEBUG]: DevTools WebSocket Event: Page.frameStartedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "97547D41B723EF1E233C79DB0FE10EAD"
}
[**********.865][DEBUG]: DevTools WebSocket Event: Page.frameStoppedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "97547D41B723EF1E233C79DB0FE10EAD"
}
[**********.869][DEBUG]: DevTools WebSocket Event: Page.frameDetached (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "97547D41B723EF1E233C79DB0FE10EAD",
   "reason": "remove"
}
[**********.869][DEBUG]: DevTools WebSocket Event: Page.frameAttached (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "C59C5943E8F65A2AB5DB15BC3EFAB160",
   "parentFrameId": "7C06568396BD027AF2A2803C59BE8E07",
   "stack": {
      "callFrames": [ {
         "columnNumber": 81,
         "functionName": "lr",
         "lineNumber": 271,
         "scriptId": "213",
         "url": "https://accounts.google.com/gsi/client"
      }, {
         "columnNumber": 448,
         "functionName": "Kr",
         "lineNumber": 318,
         "scriptId": "213",
         "url": "https://accounts.google.com/gsi/client"
      }, {
         "columnNumber": 431,
         "functionName": "Lr",
         "lineNumber": 277,
         "scriptId": "213",
         "url": "https://accounts.google.com/gsi/client"
      }, {
         "columnNumber": 2449070,
         "functionName": "_renderGoogleSignInButton",
         "lineNumber": 0,
         "scriptId": "41",
         "url": "https://abs.twimg.com/responsive-web/client-web/main.025bbe0a.js"
      }, {
         "columnNumber": 2450144,
         "functionName": "",
         "lineNumber": 0,
         "scriptId": "41",
         "url": "https://abs.twimg.com/responsive-web/client-web/main.025bbe0a.js"
      }, {
         "columnNumber": 53213,
         "functionName": "Ua",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 100475,
         "functionName": "ki",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 100055,
         "functionName": "bi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 99959,
         "functionName": "bi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 99959,
         "functionName": "bi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 99959,
         "functionName": "bi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 99599,
         "functionName": "yi",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 111262,
         "functionName": "",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 111764,
         "functionName": "ws",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 104324,
         "functionName": "ls",
         "lineNumber": 10,
         "scriptId": "8",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-7940b00b.e74f2a9a.js"
      }, {
         "columnNumber": 1363,
         "functionName": "x",
         "lineNumber": 27,
         "scriptId": "9",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-adcb47af.11578c3a.js"
      }, {
         "columnNumber": 1895,
         "functionName": "P",
         "lineNumber": 27,
         "scriptId": "9",
         "url": "https://abs.twimg.com/responsive-web/client-web/vendor-adcb47af.11578c3a.js"
      } ]
   }
}
[**********.869][DEBUG]: DevTools WebSocket Event: Page.frameStartedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "C59C5943E8F65A2AB5DB15BC3EFAB160"
}
[**********.998][DEBUG]: DevTools WebSocket Event: Target.attachedToTarget (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "sessionId": "A1D623DFAA3B849582F758C682AC198F",
   "targetInfo": {
      "attached": true,
      "browserContextId": "49C24AE394506CB36C128C7A3AFE983C",
      "canAccessOpener": false,
      "targetId": "C59C5943E8F65A2AB5DB15BC3EFAB160",
      "title": "",
      "type": "iframe",
      "url": ""
   },
   "waitingForDebugger": false
}
[**********.998][DEBUG]: DevTools WebSocket Command: Page.addScriptToEvaluateOnNewDocument (id=33) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "source": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;}) ();"
}
[**********.998][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=34) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "expression": "(function () {window.cdc_adoQpoasnfa76pfcZLmcfl_Array = window.Array;window.cdc_adoQpoasnfa76pfcZLmcfl_Promise = window.Promise;window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol = window.Symbol;}) ();"
}
[**********.998][DEBUG]: DevTools WebSocket Command: Target.setAutoAttach (id=35) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "autoAttach": true,
   "flatten": true,
   "waitForDebuggerOnStart": false
}
[**********.998][DEBUG]: DevTools WebSocket Command: Page.enable (id=36) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
}
[**********.999][DEBUG]: DevTools WebSocket Command: Page.enable (id=37) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.002][DEBUG]: DevTools WebSocket Response: Page.enable (id=37) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.006][DEBUG]: DevTools WebSocket Event: Target.targetInfoChanged (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "targetInfo": {
      "attached": true,
      "browserContextId": "49C24AE394506CB36C128C7A3AFE983C",
      "canAccessOpener": false,
      "targetId": "C59C5943E8F65A2AB5DB15BC3EFAB160",
      "title": "https://accounts.google.com/gsi/button?theme=outline&size=large&shape=circle&logo_alignment=center&text=signin_with&width=300&is_fedcm_supported=false&client_id=***********-kgt0hghf445lmcmhijv46b71...",
      "type": "iframe",
      "url": "https://accounts.google.com/gsi/button?theme=outline&size=large&shape=circle&logo_alignment=center&text=signin_with&width=300&is_fedcm_supported=false&client_id=***********-kgt0hghf445lmcmhijv46b71..."
   }
}
[**********.006][DEBUG]: DevTools WebSocket Event: Page.frameStoppedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "C59C5943E8F65A2AB5DB15BC3EFAB160"
}
[**********.007][DEBUG]: DevTools WebSocket Event: Page.frameDetached (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "C59C5943E8F65A2AB5DB15BC3EFAB160",
   "reason": "swap"
}
[**********.012][DEBUG]: DevTools WebSocket Response: Page.addScriptToEvaluateOnNewDocument (id=33) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "identifier": "1"
}
[**********.013][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=34) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "result": {
      "type": "undefined"
   }
}
[**********.013][DEBUG]: DevTools WebSocket Response: Target.setAutoAttach (id=35) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
}
[**********.013][DEBUG]: DevTools WebSocket Response: Page.enable (id=36) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
}
[**********.013][DEBUG]: DevTools WebSocket Command: Runtime.enable (id=38) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
}
[**********.018][DEBUG]: DevTools WebSocket Event: Runtime.executionContextCreated (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "context": {
      "auxData": {
         "frameId": "C59C5943E8F65A2AB5DB15BC3EFAB160",
         "isDefault": true,
         "type": "default"
      },
      "id": 1,
      "name": "",
      "origin": "https://accounts.google.com",
      "uniqueId": "3463850929880829148.-7641521312033532256"
   }
}
[**********.018][DEBUG]: DevTools WebSocket Response: Runtime.enable (id=38) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
}
[**********.018][DEBUG]: DevTools WebSocket Command: Page.enable (id=39) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
}
[**********.019][DEBUG]: DevTools WebSocket Response: Page.enable (id=39) (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
}
[**********.121][DEBUG]: DevTools WebSocket Event: Page.loadEventFired (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "timestamp": 44622.479738
}
[**********.121][DEBUG]: DevTools WebSocket Event: Page.frameStoppedLoading (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "frameId": "C59C5943E8F65A2AB5DB15BC3EFAB160"
}
[**********.121][DEBUG]: DevTools WebSocket Event: Page.domContentEventFired (session_id=A1D623DFAA3B849582F758C682AC198F) A1D623DFAA3B849582F758C682AC198F {
   "timestamp": 44622.48613
}
[**********.675][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ExecuteScript {
   "args": [  ],
   "script": "window.localStorage.clear();"
}
[**********.675][INFO]: Waiting for pending navigations...
[**********.675][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=40) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.676][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=40) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.676][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=41) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "document",
   "objectGroup": "ea3fc6a7-6cbd-4516-af3a-54ddddb10b27"
}
[**********.678][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=41) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "className": "HTMLDocument",
      "description": "#document",
      "objectId": "4409707110441190648.1.1",
      "subtype": "node",
      "type": "object"
   }
}
[**********.678][DEBUG]: DevTools WebSocket Command: DOM.describeNode (id=42) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "objectId": "4409707110441190648.1.1"
}
[**********.679][DEBUG]: DevTools WebSocket Response: DOM.describeNode (id=42) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "node": {
      "backendNodeId": 1,
      "baseURL": "https://x.com/i/flow/login",
      "childNodeCount": 2,
      "compatibilityMode": "NoQuirksMode",
      "documentURL": "https://x.com/i/flow/login",
      "localName": "",
      "nodeId": 0,
      "nodeName": "#document",
      "nodeType": 9,
      "nodeValue": "",
      "xmlVersion": ""
   }
}
[**********.679][DEBUG]: DevTools WebSocket Command: Runtime.releaseObjectGroup (id=43) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "objectGroup": "ea3fc6a7-6cbd-4516-af3a-54ddddb10b27"
}
[**********.679][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=44) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true
}
[**********.680][DEBUG]: DevTools WebSocket Response: Runtime.releaseObjectGroup (id=43) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.680][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=44) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "complete"
   }
}
[**********.680][INFO]: Done waiting for pending navigations. Status: ok
[**********.680][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=45) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.685][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=45) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": null
      }
   }
}
[**********.685][INFO]: Waiting for pending navigations...
[**********.685][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=46) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.686][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=46) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.686][INFO]: Done waiting for pending navigations. Status: ok
[**********.686][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ExecuteScript null
[**********.689][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ExecuteScript {
   "args": [  ],
   "script": "window.sessionStorage.clear();"
}
[**********.689][INFO]: Waiting for pending navigations...
[**********.689][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=47) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.690][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=47) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.690][INFO]: Done waiting for pending navigations. Status: ok
[**********.690][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=48) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.695][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=48) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": null
      }
   }
}
[**********.695][INFO]: Waiting for pending navigations...
[**********.695][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=49) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.696][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=49) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.696][INFO]: Done waiting for pending navigations. Status: ok
[**********.696][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ExecuteScript null
[**********.699][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ExecuteScript {
   "args": [  ],
   "script": "\n            document.cookie.split(';').forEach(function(c) {\n                document.cookie = c.trim().split('=')[0] + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC;';\n            });\n        "
}
[**********.699][INFO]: Waiting for pending navigations...
[**********.699][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=50) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.700][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=50) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.700][INFO]: Done waiting for pending navigations. Status: ok
[**********.701][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=51) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.706][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=51) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": null
      }
   }
}
[**********.706][INFO]: Waiting for pending navigations...
[**********.706][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=52) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.707][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=52) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.707][INFO]: Done waiting for pending navigations. Status: ok
[**********.707][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ExecuteScript null
[**********.709][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND FindElement {
   "using": "xpath",
   "value": "//input[@autocomplete=\"username\"]"
}
[**********.709][INFO]: Waiting for pending navigations...
[**********.709][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=53) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.710][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=53) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.710][INFO]: Done waiting for pending navigations. Status: ok
[**********.712][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=54) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.729][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=54) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "element-6066-11e4-a52e-4f735466cecf": "450c0d87-d327-494c-905d-c28d73d4a729"
         }
      }
   }
}
[**********.729][INFO]: Waiting for pending navigations...
[**********.729][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=55) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.730][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=55) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.731][INFO]: Done waiting for pending navigations. Status: ok
[**********.731][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE FindElement {
   "element-6066-11e4-a52e-4f735466cecf": "450c0d87-d327-494c-905d-c28d73d4a729"
}
[**********.733][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ClearElement {
   "id": "450c0d87-d327-494c-905d-c28d73d4a729"
}
[**********.733][INFO]: Waiting for pending navigations...
[**********.733][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=56) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.734][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=56) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.735][INFO]: Done waiting for pending navigations. Status: ok
[**********.735][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=57) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.739][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=57) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "input"
      }
   }
}
[**********.740][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=58) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.752][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=58) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "text"
      }
   }
}
[**********.752][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=59) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.757][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=59) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": false
      }
   }
}
[**********.758][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=60) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.772][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=60) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.773][INFO]: 
	=== NOTE: ===
	The Clear command in ChromeDriver 2.43 and above
	has been updated to conform to the current standard,
	including raising blur event after clearing.

[**********.775][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=61) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.794][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=61) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": null
      }
   }
}
[**********.794][INFO]: Waiting for pending navigations...
[**********.794][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=62) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.795][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=62) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.795][INFO]: Done waiting for pending navigations. Status: ok
[**********.795][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ClearElement
[**********.798][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND TypeElement {
   "id": "450c0d87-d327-494c-905d-c28d73d4a729",
   "text": "BuzzHaber",
   "value": [ "B", "u", "z", "z", "H", "a", "b", "e", "r" ]
}
[**********.798][INFO]: Waiting for pending navigations...
[**********.798][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=63) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.799][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=63) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.799][INFO]: Done waiting for pending navigations. Status: ok
[**********.801][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=64) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.812][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=64) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "INPUT"
      }
   }
}
[**********.813][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=65) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.819][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=65) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "text"
      }
   }
}
[**********.819][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=66) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.823][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=66) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": false
      }
   }
}
[**********.824][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=67) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.829][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=67) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "INPUT"
      }
   }
}
[**********.829][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=68) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.hasFocus()",
   "returnByValue": true
}
[**********.830][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=68) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "boolean",
      "value": true
   }
}
[**********.830][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=69) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.834][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=69) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "element-6066-11e4-a52e-4f735466cecf": "3429c65c-1328-4592-8b4b-ccc7e7bf0d8b"
         }
      }
   }
}
[**********.835][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=70) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.842][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=70) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.844][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=71) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.853][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=71) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.853][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=72) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.864][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=72) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": null
      }
   }
}
[**********.865][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=73) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.876][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=73) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": null
      }
   }
}
[**********.876][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=74) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "ShiftLeft",
   "key": "Shift",
   "modifiers": 0,
   "text": "",
   "type": "rawKeyDown",
   "unmodifiedText": "",
   "windowsVirtualKeyCode": 16
}
[**********.876][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=75) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyB",
   "key": "B",
   "modifiers": 8,
   "text": "B",
   "type": "rawKeyDown",
   "unmodifiedText": "b",
   "windowsVirtualKeyCode": 66
}
[**********.876][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=76) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyB",
   "key": "B",
   "modifiers": 8,
   "text": "B",
   "type": "char",
   "unmodifiedText": "b",
   "windowsVirtualKeyCode": 66
}
[**********.876][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=77) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyB",
   "key": "B",
   "modifiers": 8,
   "text": "B",
   "type": "keyUp",
   "unmodifiedText": "b",
   "windowsVirtualKeyCode": 66
}
[**********.876][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=78) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "ShiftLeft",
   "key": "Shift",
   "modifiers": 0,
   "text": "",
   "type": "keyUp",
   "unmodifiedText": "",
   "windowsVirtualKeyCode": 16
}
[**********.877][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=79) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyU",
   "key": "u",
   "modifiers": 0,
   "text": "u",
   "type": "rawKeyDown",
   "unmodifiedText": "u",
   "windowsVirtualKeyCode": 85
}
[**********.877][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=80) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyU",
   "key": "u",
   "modifiers": 0,
   "text": "u",
   "type": "char",
   "unmodifiedText": "u",
   "windowsVirtualKeyCode": 85
}
[**********.877][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=81) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyU",
   "key": "u",
   "modifiers": 0,
   "text": "u",
   "type": "keyUp",
   "unmodifiedText": "u",
   "windowsVirtualKeyCode": 85
}
[**********.877][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=82) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyZ",
   "key": "z",
   "modifiers": 0,
   "text": "z",
   "type": "rawKeyDown",
   "unmodifiedText": "z",
   "windowsVirtualKeyCode": 90
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=83) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyZ",
   "key": "z",
   "modifiers": 0,
   "text": "z",
   "type": "char",
   "unmodifiedText": "z",
   "windowsVirtualKeyCode": 90
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=84) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyZ",
   "key": "z",
   "modifiers": 0,
   "text": "z",
   "type": "keyUp",
   "unmodifiedText": "z",
   "windowsVirtualKeyCode": 90
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=85) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyZ",
   "key": "z",
   "modifiers": 0,
   "text": "z",
   "type": "rawKeyDown",
   "unmodifiedText": "z",
   "windowsVirtualKeyCode": 90
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=86) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyZ",
   "key": "z",
   "modifiers": 0,
   "text": "z",
   "type": "char",
   "unmodifiedText": "z",
   "windowsVirtualKeyCode": 90
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=87) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyZ",
   "key": "z",
   "modifiers": 0,
   "text": "z",
   "type": "keyUp",
   "unmodifiedText": "z",
   "windowsVirtualKeyCode": 90
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=88) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "ShiftLeft",
   "key": "Shift",
   "modifiers": 0,
   "text": "",
   "type": "rawKeyDown",
   "unmodifiedText": "",
   "windowsVirtualKeyCode": 16
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=89) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyH",
   "key": "H",
   "modifiers": 8,
   "text": "H",
   "type": "rawKeyDown",
   "unmodifiedText": "h",
   "windowsVirtualKeyCode": 72
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=90) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyH",
   "key": "H",
   "modifiers": 8,
   "text": "H",
   "type": "char",
   "unmodifiedText": "h",
   "windowsVirtualKeyCode": 72
}
[**********.878][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=91) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyH",
   "key": "H",
   "modifiers": 8,
   "text": "H",
   "type": "keyUp",
   "unmodifiedText": "h",
   "windowsVirtualKeyCode": 72
}
[**********.879][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=92) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "ShiftLeft",
   "key": "Shift",
   "modifiers": 0,
   "text": "",
   "type": "keyUp",
   "unmodifiedText": "",
   "windowsVirtualKeyCode": 16
}
[**********.879][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=93) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyA",
   "key": "a",
   "modifiers": 0,
   "text": "a",
   "type": "rawKeyDown",
   "unmodifiedText": "a",
   "windowsVirtualKeyCode": 65
}
[**********.879][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=94) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyA",
   "key": "a",
   "modifiers": 0,
   "text": "a",
   "type": "char",
   "unmodifiedText": "a",
   "windowsVirtualKeyCode": 65
}
[**********.879][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=95) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyA",
   "key": "a",
   "modifiers": 0,
   "text": "a",
   "type": "keyUp",
   "unmodifiedText": "a",
   "windowsVirtualKeyCode": 65
}
[**********.879][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=96) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyB",
   "key": "b",
   "modifiers": 0,
   "text": "b",
   "type": "rawKeyDown",
   "unmodifiedText": "b",
   "windowsVirtualKeyCode": 66
}
[**********.879][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=97) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyB",
   "key": "b",
   "modifiers": 0,
   "text": "b",
   "type": "char",
   "unmodifiedText": "b",
   "windowsVirtualKeyCode": 66
}
[**********.879][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=98) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyB",
   "key": "b",
   "modifiers": 0,
   "text": "b",
   "type": "keyUp",
   "unmodifiedText": "b",
   "windowsVirtualKeyCode": 66
}
[**********.880][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=99) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyE",
   "key": "e",
   "modifiers": 0,
   "text": "e",
   "type": "rawKeyDown",
   "unmodifiedText": "e",
   "windowsVirtualKeyCode": 69
}
[**********.880][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=100) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyE",
   "key": "e",
   "modifiers": 0,
   "text": "e",
   "type": "char",
   "unmodifiedText": "e",
   "windowsVirtualKeyCode": 69
}
[**********.880][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=101) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyE",
   "key": "e",
   "modifiers": 0,
   "text": "e",
   "type": "keyUp",
   "unmodifiedText": "e",
   "windowsVirtualKeyCode": 69
}
[**********.880][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=102) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyR",
   "key": "r",
   "modifiers": 0,
   "text": "r",
   "type": "rawKeyDown",
   "unmodifiedText": "r",
   "windowsVirtualKeyCode": 82
}
[**********.880][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=103) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyR",
   "key": "r",
   "modifiers": 0,
   "text": "r",
   "type": "char",
   "unmodifiedText": "r",
   "windowsVirtualKeyCode": 82
}
[**********.880][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=104) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyR",
   "key": "r",
   "modifiers": 0,
   "text": "r",
   "type": "keyUp",
   "unmodifiedText": "r",
   "windowsVirtualKeyCode": 82
}
[**********.880][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=74) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.881][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=75) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.904][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=76) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.905][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=77) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.906][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=78) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.906][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=79) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.928][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=80) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.928][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=81) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.928][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=82) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.950][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=83) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.950][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=84) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.951][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=85) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.971][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=86) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.971][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=87) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.972][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=88) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.972][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=89) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.992][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=90) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.992][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=91) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.992][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=92) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.993][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=93) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.013][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=94) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.013][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=95) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.014][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=96) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.038][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=97) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.038][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=98) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.039][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=99) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.059][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=100) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.059][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=101) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.059][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=102) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.078][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=103) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.078][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=104) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127460.078][INFO]: Waiting for pending navigations...
[1749127460.078][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=105) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[1749127460.085][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=105) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[1749127460.085][INFO]: Done waiting for pending navigations. Status: ok
[1749127460.085][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE TypeElement
[**********.088][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND FindElement {
   "using": "xpath",
   "value": "//span[text()='Next']/ancestor::button | //span[text()='İleri']/ancestor::button"
}
[**********.088][INFO]: Waiting for pending navigations...
[**********.088][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=106) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.090][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=106) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.090][INFO]: Done waiting for pending navigations. Status: ok
[**********.093][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=107) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.113][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=107) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "element-6066-11e4-a52e-4f735466cecf": "6d5ff0be-83e4-41c9-af82-60b7d4b7d38b"
         }
      }
   }
}
[**********.113][INFO]: Waiting for pending navigations...
[**********.113][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=108) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.114][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=108) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.114][INFO]: Done waiting for pending navigations. Status: ok
[**********.114][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE FindElement {
   "element-6066-11e4-a52e-4f735466cecf": "6d5ff0be-83e4-41c9-af82-60b7d4b7d38b"
}
[**********.121][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ExecuteScript {
   "args": [ {
      "element-6066-11e4-a52e-4f735466cecf": "6d5ff0be-83e4-41c9-af82-60b7d4b7d38b"
   } ],
   "script": "/* isDisplayed */return (function(){return (function(){var k=this||self;function aa(a){return\"string\"==typeof a}function ba(a,b){a=a.split(\".\");var c=k;a[0]in c||\"undefined\"==typeof c.execScript||c.execScript(\"var \"+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b}\nfunction ca(a){var b=typeof a;if(\"object\"==b)if(a){if(a instanceof Array)return\"array\";if(a instanceof Object)return b;var c=Object.prototype.toString.call(a);if(\"[object Window]\"==c)return\"object\";if(\"[object Array]\"==c||\"number\"==typeof a.length&&\"undefined\"!=typeof a.splice&&\"undefined\"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable(\"splice\"))return\"array\";if(\"[object Function]\"==c||\"undefined\"!=typeof a.call&&\"undefined\"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable(\"call\"))return\"function\"}else return\"null\";\nelse if(\"function\"==b&&\"undefined\"==typeof a.call)return\"object\";return b}function da(a,b,c){return a.call.apply(a.bind,arguments)}function ea(a,b,c){if(!a)throw Error();if(2\u003Carguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}\nfunction fa(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf(\"native code\")?fa=da:fa=ea;return fa.apply(null,arguments)}function ha(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function l(a,b){function c(){}c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};/*\n\n The MIT License\n\n Copyright (c) 2007 Cybozu Labs, Inc.\n Copyright (c) 2012 Google Inc.\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to\n deal in the Software without restriction, including without limitation the\n rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n sell copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n IN THE SOFTWARE.\n*/\nfunction ia(a,b,c){this.a=a;this.b=b||1;this.f=c||1};var ja=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(\"string\"===typeof a)return\"string\"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c\u003Ca.length;c++)if(c in a&&a[c]===b)return c;return-1},n=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=\"string\"===typeof a?a.split(\"\"):a,e=0;e\u003Cc;e++)e in d&&b.call(void 0,d[e],e,a)},ka=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,\nb,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=\"string\"===typeof a?a.split(\"\"):a,g=0;g\u003Cc;g++)if(g in f){var h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d},la=Array.prototype.reduce?function(a,b,c){return Array.prototype.reduce.call(a,b,c)}:function(a,b,c){var d=c;n(a,function(e,f){d=b.call(void 0,d,e,f,a)});return d},ma=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=\"string\"===typeof a?a.split(\"\"):a,e=0;e\u003Cc;e++)if(e in\nd&&b.call(void 0,d[e],e,a))return!0;return!1},na=Array.prototype.every?function(a,b){return Array.prototype.every.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=\"string\"===typeof a?a.split(\"\"):a,e=0;e\u003Cc;e++)if(e in d&&!b.call(void 0,d[e],e,a))return!1;return!0};function oa(a,b){a:{for(var c=a.length,d=\"string\"===typeof a?a.split(\"\"):a,e=0;e\u003Cc;e++)if(e in d&&b.call(void 0,d[e],e,a)){b=e;break a}b=-1}return 0>b?null:\"string\"===typeof a?a.charAt(b):a[b]}\nfunction pa(a){return Array.prototype.concat.apply([],arguments)}function qa(a,b,c){return 2>=arguments.length?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};var ra=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\\s\\xa0]*([\\s\\S]*?)[\\s\\xa0]*$/.exec(a)[1]};function sa(a,b){return a\u003Cb?-1:a>b?1:0};var t;a:{var ta=k.navigator;if(ta){var ua=ta.userAgent;if(ua){t=ua;break a}}t=\"\"}function u(a){return-1!=t.indexOf(a)};function va(){return u(\"Firefox\")||u(\"FxiOS\")}function wa(){return(u(\"Chrome\")||u(\"CriOS\"))&&!u(\"Edge\")};function xa(a){return String(a).replace(/\\-([a-z])/g,function(b,c){return c.toUpperCase()})};function ya(){return u(\"iPhone\")&&!u(\"iPod\")&&!u(\"iPad\")};function za(a,b){var c=Aa;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};var Ba=u(\"Opera\"),v=u(\"Trident\")||u(\"MSIE\"),Ca=u(\"Edge\"),Da=u(\"Gecko\")&&!(-1!=t.toLowerCase().indexOf(\"webkit\")&&!u(\"Edge\"))&&!(u(\"Trident\")||u(\"MSIE\"))&&!u(\"Edge\"),Ea=-1!=t.toLowerCase().indexOf(\"webkit\")&&!u(\"Edge\");function Fa(){var a=k.document;return a?a.documentMode:void 0}var Ga;\na:{var Ha=\"\",Ia=function(){var a=t;if(Da)return/rv:([^\\);]+)(\\)|;)/.exec(a);if(Ca)return/Edge\\/([\\d\\.]+)/.exec(a);if(v)return/\\b(?:MSIE|rv)[: ]([^\\);]+)(\\)|;)/.exec(a);if(Ea)return/WebKit\\/(\\S+)/.exec(a);if(Ba)return/(?:Version)[ \\/]?(\\S+)/.exec(a)}();Ia&&(Ha=Ia?Ia[1]:\"\");if(v){var Ja=Fa();if(null!=Ja&&Ja>parseFloat(Ha)){Ga=String(Ja);break a}}Ga=Ha}var Aa={};\nfunction Ka(a){return za(a,function(){for(var b=0,c=ra(String(Ga)).split(\".\"),d=ra(String(a)).split(\".\"),e=Math.max(c.length,d.length),f=0;0==b&&f\u003Ce;f++){var g=c[f]||\"\",h=d[f]||\"\";do{g=/(\\d*)(\\D*)(.*)/.exec(g)||[\"\",\"\",\"\",\"\"];h=/(\\d*)(\\D*)(.*)/.exec(h)||[\"\",\"\",\"\",\"\"];if(0==g[0].length&&0==h[0].length)break;b=sa(0==g[1].length?0:parseInt(g[1],10),0==h[1].length?0:parseInt(h[1],10))||sa(0==g[2].length,0==h[2].length)||sa(g[2],h[2]);g=g[3];h=h[3]}while(0==b)}return 0\u003C=b})}var La;\nLa=k.document&&v?Fa():void 0;var x=v&&!(9\u003C=Number(La)),Ma=v&&!(8\u003C=Number(La));function Na(a,b,c,d){this.a=a;this.nodeName=c;this.nodeValue=d;this.nodeType=2;this.parentNode=this.ownerElement=b}function Oa(a,b){var c=Ma&&\"href\"==b.nodeName?a.getAttribute(b.nodeName,2):b.nodeValue;return new Na(b,a,b.nodeName,c)};function Pa(a){this.b=a;this.a=0}function Qa(a){a=a.match(Ra);for(var b=0;b\u003Ca.length;b++)Sa.test(a[b])&&a.splice(b,1);return new Pa(a)}var Ra=/\\$?(?:(?![0-9-\\.])(?:\\*|[\\w-\\.]+):)?(?![0-9-\\.])(?:\\*|[\\w-\\.]+)|\\/\\/|\\.\\.|::|\\d+(?:\\.\\d*)?|\\.\\d+|\"[^\"]*\"|'[^']*'|[!\u003C>]=|\\s+|./g,Sa=/^\\s/;function y(a,b){return a.b[a.a+(b||0)]}function z(a){return a.b[a.a++]}function Ta(a){return a.b.length\u003C=a.a};function Ua(a,b){this.x=void 0!==a?a:0;this.y=void 0!==b?b:0}Ua.prototype.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};Ua.prototype.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};Ua.prototype.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};function Va(a,b){this.width=a;this.height=b}Va.prototype.aspectRatio=function(){return this.width/this.height};Va.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};Va.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};Va.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};function Wa(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if(\"undefined\"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a}\nfunction Xa(a,b){if(a==b)return 0;if(a.compareDocumentPosition)return a.compareDocumentPosition(b)&2?1:-1;if(v&&!(9\u003C=Number(La))){if(9==a.nodeType)return-1;if(9==b.nodeType)return 1}if(\"sourceIndex\"in a||a.parentNode&&\"sourceIndex\"in a.parentNode){var c=1==a.nodeType,d=1==b.nodeType;if(c&&d)return a.sourceIndex-b.sourceIndex;var e=a.parentNode,f=b.parentNode;return e==f?Ya(a,b):!c&&Wa(e,b)?-1*Za(a,b):!d&&Wa(f,a)?Za(b,a):(c?a.sourceIndex:e.sourceIndex)-(d?b.sourceIndex:f.sourceIndex)}d=A(a);c=d.createRange();\nc.selectNode(a);c.collapse(!0);a=d.createRange();a.selectNode(b);a.collapse(!0);return c.compareBoundaryPoints(k.Range.START_TO_END,a)}function Za(a,b){var c=a.parentNode;if(c==b)return-1;for(;b.parentNode!=c;)b=b.parentNode;return Ya(b,a)}function Ya(a,b){for(;b=b.previousSibling;)if(b==a)return-1;return 1}function A(a){return 9==a.nodeType?a:a.ownerDocument||a.document}function $a(a,b){a&&(a=a.parentNode);for(var c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null}\nfunction ab(a){this.a=a||k.document||document}ab.prototype.getElementsByTagName=function(a,b){return(b||this.a).getElementsByTagName(String(a))};function B(a){var b=null,c=a.nodeType;1==c&&(b=a.textContent,b=void 0==b||null==b?a.innerText:b,b=void 0==b||null==b?\"\":b);if(\"string\"!=typeof b)if(x&&\"title\"==a.nodeName.toLowerCase()&&1==c)b=a.text;else if(9==c||1==c){a=9==c?a.documentElement:a.firstChild;c=0;var d=[];for(b=\"\";a;){do 1!=a.nodeType&&(b+=a.nodeValue),x&&\"title\"==a.nodeName.toLowerCase()&&(b+=a.text),d[c++]=a;while(a=a.firstChild);for(;c&&!(a=d[--c].nextSibling););}}else b=a.nodeValue;return b}\nfunction C(a,b,c){if(null===b)return!0;try{if(!a.getAttribute)return!1}catch(d){return!1}Ma&&\"class\"==b&&(b=\"className\");return null==c?!!a.getAttribute(b):a.getAttribute(b,2)==c}function bb(a,b,c,d,e){return(x?cb:db).call(null,a,b,aa(c)?c:null,aa(d)?d:null,e||new E)}\nfunction cb(a,b,c,d,e){if(a instanceof F||8==a.b||c&&null===a.b){var f=b.all;if(!f)return e;a=eb(a);if(\"*\"!=a&&(f=b.getElementsByTagName(a),!f))return e;if(c){for(var g=[],h=0;b=f[h++];)C(b,c,d)&&g.push(b);f=g}for(h=0;b=f[h++];)\"*\"==a&&\"!\"==b.tagName||e.add(b);return e}gb(a,b,c,d,e);return e}\nfunction db(a,b,c,d,e){b.getElementsByName&&d&&\"name\"==c&&!v?(b=b.getElementsByName(d),n(b,function(f){a.a(f)&&e.add(f)})):b.getElementsByClassName&&d&&\"class\"==c?(b=b.getElementsByClassName(d),n(b,function(f){f.className==d&&a.a(f)&&e.add(f)})):a instanceof G?gb(a,b,c,d,e):b.getElementsByTagName&&(b=b.getElementsByTagName(a.f()),n(b,function(f){C(f,c,d)&&e.add(f)}));return e}\nfunction hb(a,b,c,d,e){var f;if((a instanceof F||8==a.b||c&&null===a.b)&&(f=b.childNodes)){var g=eb(a);if(\"*\"!=g&&(f=ka(f,function(h){return h.tagName&&h.tagName.toLowerCase()==g}),!f))return e;c&&(f=ka(f,function(h){return C(h,c,d)}));n(f,function(h){\"*\"==g&&(\"!\"==h.tagName||\"*\"==g&&1!=h.nodeType)||e.add(h)});return e}return ib(a,b,c,d,e)}function ib(a,b,c,d,e){for(b=b.firstChild;b;b=b.nextSibling)C(b,c,d)&&a.a(b)&&e.add(b);return e}\nfunction gb(a,b,c,d,e){for(b=b.firstChild;b;b=b.nextSibling)C(b,c,d)&&a.a(b)&&e.add(b),gb(a,b,c,d,e)}function eb(a){if(a instanceof G){if(8==a.b)return\"!\";if(null===a.b)return\"*\"}return a.f()};function E(){this.b=this.a=null;this.l=0}function jb(a){this.f=a;this.a=this.b=null}function kb(a,b){if(!a.a)return b;if(!b.a)return a;var c=a.a;b=b.a;for(var d=null,e,f=0;c&&b;){e=c.f;var g=b.f;e==g||e instanceof Na&&g instanceof Na&&e.a==g.a?(e=c,c=c.a,b=b.a):0\u003CXa(c.f,b.f)?(e=b,b=b.a):(e=c,c=c.a);(e.b=d)?d.a=e:a.a=e;d=e;f++}for(e=c||b;e;)e.b=d,d=d.a=e,f++,e=e.a;a.b=d;a.l=f;return a}function lb(a,b){b=new jb(b);b.a=a.a;a.b?a.a.b=b:a.a=a.b=b;a.a=b;a.l++}\nE.prototype.add=function(a){a=new jb(a);a.b=this.b;this.a?this.b.a=a:this.a=this.b=a;this.b=a;this.l++};function mb(a){return(a=a.a)?a.f:null}function nb(a){return(a=mb(a))?B(a):\"\"}function H(a,b){return new ob(a,!!b)}function ob(a,b){this.f=a;this.b=(this.s=b)?a.b:a.a;this.a=null}function I(a){var b=a.b;if(null==b)return null;var c=a.a=b;a.b=a.s?b.b:b.a;return c.f};function J(a){this.i=a;this.b=this.g=!1;this.f=null}function K(a){return\"\\n  \"+a.toString().split(\"\\n\").join(\"\\n  \")}function pb(a,b){a.g=b}function qb(a,b){a.b=b}function N(a,b){a=a.a(b);return a instanceof E?+nb(a):+a}function O(a,b){a=a.a(b);return a instanceof E?nb(a):\"\"+a}function rb(a,b){a=a.a(b);return a instanceof E?!!a.l:!!a};function sb(a,b,c){J.call(this,a.i);this.c=a;this.h=b;this.o=c;this.g=b.g||c.g;this.b=b.b||c.b;this.c==tb&&(c.b||c.g||4==c.i||0==c.i||!b.f?b.b||b.g||4==b.i||0==b.i||!c.f||(this.f={name:c.f.name,u:b}):this.f={name:b.f.name,u:c})}l(sb,J);\nfunction ub(a,b,c,d,e){b=b.a(d);c=c.a(d);var f;if(b instanceof E&&c instanceof E){b=H(b);for(d=I(b);d;d=I(b))for(e=H(c),f=I(e);f;f=I(e))if(a(B(d),B(f)))return!0;return!1}if(b instanceof E||c instanceof E){b instanceof E?(e=b,d=c):(e=c,d=b);f=H(e);for(var g=typeof d,h=I(f);h;h=I(f)){switch(g){case \"number\":h=+B(h);break;case \"boolean\":h=!!B(h);break;case \"string\":h=B(h);break;default:throw Error(\"Illegal primitive type for comparison.\");}if(e==b&&a(h,d)||e==c&&a(d,h))return!0}return!1}return e?\"boolean\"==\ntypeof b||\"boolean\"==typeof c?a(!!b,!!c):\"number\"==typeof b||\"number\"==typeof c?a(+b,+c):a(b,c):a(+b,+c)}sb.prototype.a=function(a){return this.c.m(this.h,this.o,a)};sb.prototype.toString=function(){var a=\"Binary Expression: \"+this.c;a+=K(this.h);return a+=K(this.o)};function vb(a,b,c,d){this.I=a;this.D=b;this.i=c;this.m=d}vb.prototype.toString=function(){return this.I};var wb={};\nfunction P(a,b,c,d){if(wb.hasOwnProperty(a))throw Error(\"Binary operator already created: \"+a);a=new vb(a,b,c,d);return wb[a.toString()]=a}P(\"div\",6,1,function(a,b,c){return N(a,c)/N(b,c)});P(\"mod\",6,1,function(a,b,c){return N(a,c)%N(b,c)});P(\"*\",6,1,function(a,b,c){return N(a,c)*N(b,c)});P(\"+\",5,1,function(a,b,c){return N(a,c)+N(b,c)});P(\"-\",5,1,function(a,b,c){return N(a,c)-N(b,c)});P(\"\u003C\",4,2,function(a,b,c){return ub(function(d,e){return d\u003Ce},a,b,c)});\nP(\">\",4,2,function(a,b,c){return ub(function(d,e){return d>e},a,b,c)});P(\"\u003C=\",4,2,function(a,b,c){return ub(function(d,e){return d\u003C=e},a,b,c)});P(\">=\",4,2,function(a,b,c){return ub(function(d,e){return d>=e},a,b,c)});var tb=P(\"=\",3,2,function(a,b,c){return ub(function(d,e){return d==e},a,b,c,!0)});P(\"!=\",3,2,function(a,b,c){return ub(function(d,e){return d!=e},a,b,c,!0)});P(\"and\",2,2,function(a,b,c){return rb(a,c)&&rb(b,c)});P(\"or\",1,2,function(a,b,c){return rb(a,c)||rb(b,c)});function xb(a,b){if(b.a.length&&4!=a.i)throw Error(\"Primary expression must evaluate to nodeset if filter has predicate(s).\");J.call(this,a.i);this.c=a;this.h=b;this.g=a.g;this.b=a.b}l(xb,J);xb.prototype.a=function(a){a=this.c.a(a);return yb(this.h,a)};xb.prototype.toString=function(){var a=\"Filter:\"+K(this.c);return a+=K(this.h)};function zb(a,b){if(b.length\u003Ca.C)throw Error(\"Function \"+a.j+\" expects at least\"+a.C+\" arguments, \"+b.length+\" given\");if(null!==a.B&&b.length>a.B)throw Error(\"Function \"+a.j+\" expects at most \"+a.B+\" arguments, \"+b.length+\" given\");a.H&&n(b,function(c,d){if(4!=c.i)throw Error(\"Argument \"+d+\" to function \"+a.j+\" is not of type Nodeset: \"+c);});J.call(this,a.i);this.v=a;this.c=b;pb(this,a.g||ma(b,function(c){return c.g}));qb(this,a.G&&!b.length||a.F&&!!b.length||ma(b,function(c){return c.b}))}\nl(zb,J);zb.prototype.a=function(a){return this.v.m.apply(null,pa(a,this.c))};zb.prototype.toString=function(){var a=\"Function: \"+this.v;if(this.c.length){var b=la(this.c,function(c,d){return c+K(d)},\"Arguments:\");a+=K(b)}return a};function Ab(a,b,c,d,e,f,g,h){this.j=a;this.i=b;this.g=c;this.G=d;this.F=!1;this.m=e;this.C=f;this.B=void 0!==g?g:f;this.H=!!h}Ab.prototype.toString=function(){return this.j};var Bb={};\nfunction Q(a,b,c,d,e,f,g,h){if(Bb.hasOwnProperty(a))throw Error(\"Function already created: \"+a+\".\");Bb[a]=new Ab(a,b,c,d,e,f,g,h)}Q(\"boolean\",2,!1,!1,function(a,b){return rb(b,a)},1);Q(\"ceiling\",1,!1,!1,function(a,b){return Math.ceil(N(b,a))},1);Q(\"concat\",3,!1,!1,function(a,b){return la(qa(arguments,1),function(c,d){return c+O(d,a)},\"\")},2,null);Q(\"contains\",2,!1,!1,function(a,b,c){b=O(b,a);a=O(c,a);return-1!=b.indexOf(a)},2);Q(\"count\",1,!1,!1,function(a,b){return b.a(a).l},1,1,!0);\nQ(\"false\",2,!1,!1,function(){return!1},0);Q(\"floor\",1,!1,!1,function(a,b){return Math.floor(N(b,a))},1);Q(\"id\",4,!1,!1,function(a,b){function c(h){if(x){var m=e.all[h];if(m){if(m.nodeType&&h==m.id)return m;if(m.length)return oa(m,function(w){return h==w.id})}return null}return e.getElementById(h)}var d=a.a,e=9==d.nodeType?d:d.ownerDocument;a=O(b,a).split(/\\s+/);var f=[];n(a,function(h){h=c(h);!h||0\u003C=ja(f,h)||f.push(h)});f.sort(Xa);var g=new E;n(f,function(h){g.add(h)});return g},1);\nQ(\"lang\",2,!1,!1,function(){return!1},1);Q(\"last\",1,!0,!1,function(a){if(1!=arguments.length)throw Error(\"Function last expects ()\");return a.f},0);Q(\"local-name\",3,!1,!0,function(a,b){return(a=b?mb(b.a(a)):a.a)?a.localName||a.nodeName.toLowerCase():\"\"},0,1,!0);Q(\"name\",3,!1,!0,function(a,b){return(a=b?mb(b.a(a)):a.a)?a.nodeName.toLowerCase():\"\"},0,1,!0);Q(\"namespace-uri\",3,!0,!1,function(){return\"\"},0,1,!0);\nQ(\"normalize-space\",3,!1,!0,function(a,b){return(b?O(b,a):B(a.a)).replace(/[\\s\\xa0]+/g,\" \").replace(/^\\s+|\\s+$/g,\"\")},0,1);Q(\"not\",2,!1,!1,function(a,b){return!rb(b,a)},1);Q(\"number\",1,!1,!0,function(a,b){return b?N(b,a):+B(a.a)},0,1);Q(\"position\",1,!0,!1,function(a){return a.b},0);Q(\"round\",1,!1,!1,function(a,b){return Math.round(N(b,a))},1);Q(\"starts-with\",2,!1,!1,function(a,b,c){b=O(b,a);a=O(c,a);return 0==b.lastIndexOf(a,0)},2);Q(\"string\",3,!1,!0,function(a,b){return b?O(b,a):B(a.a)},0,1);\nQ(\"string-length\",1,!1,!0,function(a,b){return(b?O(b,a):B(a.a)).length},0,1);Q(\"substring\",3,!1,!1,function(a,b,c,d){c=N(c,a);if(isNaN(c)||Infinity==c||-Infinity==c)return\"\";d=d?N(d,a):Infinity;if(isNaN(d)||-Infinity===d)return\"\";c=Math.round(c)-1;var e=Math.max(c,0);a=O(b,a);return Infinity==d?a.substring(e):a.substring(e,c+Math.round(d))},2,3);Q(\"substring-after\",3,!1,!1,function(a,b,c){b=O(b,a);a=O(c,a);c=b.indexOf(a);return-1==c?\"\":b.substring(c+a.length)},2);\nQ(\"substring-before\",3,!1,!1,function(a,b,c){b=O(b,a);a=O(c,a);a=b.indexOf(a);return-1==a?\"\":b.substring(0,a)},2);Q(\"sum\",1,!1,!1,function(a,b){a=H(b.a(a));b=0;for(var c=I(a);c;c=I(a))b+=+B(c);return b},1,1,!0);Q(\"translate\",3,!1,!1,function(a,b,c,d){b=O(b,a);c=O(c,a);var e=O(d,a);a={};for(d=0;d\u003Cc.length;d++){var f=c.charAt(d);f in a||(a[f]=e.charAt(d))}c=\"\";for(d=0;d\u003Cb.length;d++)f=b.charAt(d),c+=f in a?a[f]:f;return c},3);Q(\"true\",2,!1,!1,function(){return!0},0);function G(a,b){this.h=a;this.c=void 0!==b?b:null;this.b=null;switch(a){case \"comment\":this.b=8;break;case \"text\":this.b=3;break;case \"processing-instruction\":this.b=7;break;case \"node\":break;default:throw Error(\"Unexpected argument\");}}function Cb(a){return\"comment\"==a||\"text\"==a||\"processing-instruction\"==a||\"node\"==a}G.prototype.a=function(a){return null===this.b||this.b==a.nodeType};G.prototype.f=function(){return this.h};\nG.prototype.toString=function(){var a=\"Kind Test: \"+this.h;null===this.c||(a+=K(this.c));return a};function Db(a){J.call(this,3);this.c=a.substring(1,a.length-1)}l(Db,J);Db.prototype.a=function(){return this.c};Db.prototype.toString=function(){return\"Literal: \"+this.c};function F(a,b){this.j=a.toLowerCase();a=\"*\"==this.j?\"*\":\"http://www.w3.org/1999/xhtml\";this.c=b?b.toLowerCase():a}F.prototype.a=function(a){var b=a.nodeType;if(1!=b&&2!=b)return!1;b=void 0!==a.localName?a.localName:a.nodeName;return\"*\"!=this.j&&this.j!=b.toLowerCase()?!1:\"*\"==this.c?!0:this.c==(a.namespaceURI?a.namespaceURI.toLowerCase():\"http://www.w3.org/1999/xhtml\")};F.prototype.f=function(){return this.j};\nF.prototype.toString=function(){return\"Name Test: \"+(\"http://www.w3.org/1999/xhtml\"==this.c?\"\":this.c+\":\")+this.j};function Eb(a){J.call(this,1);this.c=a}l(Eb,J);Eb.prototype.a=function(){return this.c};Eb.prototype.toString=function(){return\"Number: \"+this.c};function Fb(a,b){J.call(this,a.i);this.h=a;this.c=b;this.g=a.g;this.b=a.b;1==this.c.length&&(a=this.c[0],a.A||a.c!=Gb||(a=a.o,\"*\"!=a.f()&&(this.f={name:a.f(),u:null})))}l(Fb,J);function Hb(){J.call(this,4)}l(Hb,J);Hb.prototype.a=function(a){var b=new E;a=a.a;9==a.nodeType?b.add(a):b.add(a.ownerDocument);return b};Hb.prototype.toString=function(){return\"Root Helper Expression\"};function Ib(){J.call(this,4)}l(Ib,J);Ib.prototype.a=function(a){var b=new E;b.add(a.a);return b};Ib.prototype.toString=function(){return\"Context Helper Expression\"};\nfunction Jb(a){return\"/\"==a||\"//\"==a}Fb.prototype.a=function(a){var b=this.h.a(a);if(!(b instanceof E))throw Error(\"Filter expression must evaluate to nodeset.\");a=this.c;for(var c=0,d=a.length;c\u003Cd&&b.l;c++){var e=a[c],f=H(b,e.c.s);if(e.g||e.c!=Kb)if(e.g||e.c!=Lb){var g=I(f);for(b=e.a(new ia(g));null!=(g=I(f));)g=e.a(new ia(g)),b=kb(b,g)}else g=I(f),b=e.a(new ia(g));else{for(g=I(f);(b=I(f))&&(!g.contains||g.contains(b))&&b.compareDocumentPosition(g)&8;g=b);b=e.a(new ia(g))}}return b};\nFb.prototype.toString=function(){var a=\"Path Expression:\"+K(this.h);if(this.c.length){var b=la(this.c,function(c,d){return c+K(d)},\"Steps:\");a+=K(b)}return a};function Mb(a,b){this.a=a;this.s=!!b}\nfunction yb(a,b,c){for(c=c||0;c\u003Ca.a.length;c++)for(var d=a.a[c],e=H(b),f=b.l,g,h=0;g=I(e);h++){var m=a.s?f-h:h+1;g=d.a(new ia(g,m,f));if(\"number\"==typeof g)m=m==g;else if(\"string\"==typeof g||\"boolean\"==typeof g)m=!!g;else if(g instanceof E)m=0\u003Cg.l;else throw Error(\"Predicate.evaluate returned an unexpected type.\");if(!m){m=e;g=m.f;var w=m.a;if(!w)throw Error(\"Next must be called at least once before remove.\");var r=w.b;w=w.a;r?r.a=w:g.a=w;w?w.b=r:g.b=r;g.l--;m.a=null}}return b}\nMb.prototype.toString=function(){return la(this.a,function(a,b){return a+K(b)},\"Predicates:\")};function R(a,b,c,d){J.call(this,4);this.c=a;this.o=b;this.h=c||new Mb([]);this.A=!!d;b=this.h;b=0\u003Cb.a.length?b.a[0].f:null;a.J&&b&&(a=b.name,a=x?a.toLowerCase():a,this.f={name:a,u:b.u});a:{a=this.h;for(b=0;b\u003Ca.a.length;b++)if(c=a.a[b],c.g||1==c.i||0==c.i){a=!0;break a}a=!1}this.g=a}l(R,J);\nR.prototype.a=function(a){var b=a.a,c=this.f,d=null,e=null,f=0;c&&(d=c.name,e=c.u?O(c.u,a):null,f=1);if(this.A)if(this.g||this.c!=Nb)if(b=H((new R(Ob,new G(\"node\"))).a(a)),c=I(b))for(a=this.m(c,d,e,f);null!=(c=I(b));)a=kb(a,this.m(c,d,e,f));else a=new E;else a=bb(this.o,b,d,e),a=yb(this.h,a,f);else a=this.m(a.a,d,e,f);return a};R.prototype.m=function(a,b,c,d){a=this.c.v(this.o,a,b,c);return a=yb(this.h,a,d)};\nR.prototype.toString=function(){var a=\"Step:\"+K(\"Operator: \"+(this.A?\"//\":\"/\"));this.c.j&&(a+=K(\"Axis: \"+this.c));a+=K(this.o);if(this.h.a.length){var b=la(this.h.a,function(c,d){return c+K(d)},\"Predicates:\");a+=K(b)}return a};function Pb(a,b,c,d){this.j=a;this.v=b;this.s=c;this.J=d}Pb.prototype.toString=function(){return this.j};var Qb={};function S(a,b,c,d){if(Qb.hasOwnProperty(a))throw Error(\"Axis already created: \"+a);b=new Pb(a,b,c,!!d);return Qb[a]=b}\nS(\"ancestor\",function(a,b){for(var c=new E;b=b.parentNode;)a.a(b)&&lb(c,b);return c},!0);S(\"ancestor-or-self\",function(a,b){var c=new E;do a.a(b)&&lb(c,b);while(b=b.parentNode);return c},!0);\nvar Gb=S(\"attribute\",function(a,b){var c=new E,d=a.f();if(\"style\"==d&&x&&b.style)return c.add(new Na(b.style,b,\"style\",b.style.cssText)),c;var e=b.attributes;if(e)if(a instanceof G&&null===a.b||\"*\"==d)for(a=0;d=e[a];a++)x?d.nodeValue&&c.add(Oa(b,d)):c.add(d);else(d=e.getNamedItem(d))&&(x?d.nodeValue&&c.add(Oa(b,d)):c.add(d));return c},!1),Nb=S(\"child\",function(a,b,c,d,e){return(x?hb:ib).call(null,a,b,aa(c)?c:null,aa(d)?d:null,e||new E)},!1,!0);S(\"descendant\",bb,!1,!0);\nvar Ob=S(\"descendant-or-self\",function(a,b,c,d){var e=new E;C(b,c,d)&&a.a(b)&&e.add(b);return bb(a,b,c,d,e)},!1,!0),Kb=S(\"following\",function(a,b,c,d){var e=new E;do for(var f=b;f=f.nextSibling;)C(f,c,d)&&a.a(f)&&e.add(f),e=bb(a,f,c,d,e);while(b=b.parentNode);return e},!1,!0);S(\"following-sibling\",function(a,b){for(var c=new E;b=b.nextSibling;)a.a(b)&&c.add(b);return c},!1);S(\"namespace\",function(){return new E},!1);\nvar Rb=S(\"parent\",function(a,b){var c=new E;if(9==b.nodeType)return c;if(2==b.nodeType)return c.add(b.ownerElement),c;b=b.parentNode;a.a(b)&&c.add(b);return c},!1),Lb=S(\"preceding\",function(a,b,c,d){var e=new E,f=[];do f.unshift(b);while(b=b.parentNode);for(var g=1,h=f.length;g\u003Ch;g++){var m=[];for(b=f[g];b=b.previousSibling;)m.unshift(b);for(var w=0,r=m.length;w\u003Cr;w++)b=m[w],C(b,c,d)&&a.a(b)&&e.add(b),e=bb(a,b,c,d,e)}return e},!0,!0);\nS(\"preceding-sibling\",function(a,b){for(var c=new E;b=b.previousSibling;)a.a(b)&&lb(c,b);return c},!0);var Sb=S(\"self\",function(a,b){var c=new E;a.a(b)&&c.add(b);return c},!1);function Tb(a){J.call(this,1);this.c=a;this.g=a.g;this.b=a.b}l(Tb,J);Tb.prototype.a=function(a){return-N(this.c,a)};Tb.prototype.toString=function(){return\"Unary Expression: -\"+K(this.c)};function Ub(a){J.call(this,4);this.c=a;pb(this,ma(this.c,function(b){return b.g}));qb(this,ma(this.c,function(b){return b.b}))}l(Ub,J);Ub.prototype.a=function(a){var b=new E;n(this.c,function(c){c=c.a(a);if(!(c instanceof E))throw Error(\"Path expression must evaluate to NodeSet.\");b=kb(b,c)});return b};Ub.prototype.toString=function(){return la(this.c,function(a,b){return a+K(b)},\"Union Expression:\")};function Vb(a,b){this.a=a;this.b=b}function Yb(a){for(var b,c=[];;){T(a,\"Missing right hand side of binary expression.\");b=Zb(a);var d=z(a.a);if(!d)break;var e=(d=wb[d]||null)&&d.D;if(!e){a.a.a--;break}for(;c.length&&e\u003C=c[c.length-1].D;)b=new sb(c.pop(),c.pop(),b);c.push(b,d)}for(;c.length;)b=new sb(c.pop(),c.pop(),b);return b}function T(a,b){if(Ta(a.a))throw Error(b);}function $b(a,b){a=z(a.a);if(a!=b)throw Error(\"Bad token, expected: \"+b+\" got: \"+a);}\nfunction ac(a){a=z(a.a);if(\")\"!=a)throw Error(\"Bad token: \"+a);}function bc(a){a=z(a.a);if(2>a.length)throw Error(\"Unclosed literal string\");return new Db(a)}\nfunction cc(a){var b=[];if(Jb(y(a.a))){var c=z(a.a);var d=y(a.a);if(\"/\"==c&&(Ta(a.a)||\".\"!=d&&\"..\"!=d&&\"@\"!=d&&\"*\"!=d&&!/(?![0-9])[\\w]/.test(d)))return new Hb;d=new Hb;T(a,\"Missing next location step.\");c=dc(a,c);b.push(c)}else{a:{c=y(a.a);d=c.charAt(0);switch(d){case \"$\":throw Error(\"Variable reference not allowed in HTML XPath\");case \"(\":z(a.a);c=Yb(a);T(a,'unclosed \"(\"');$b(a,\")\");break;case '\"':case \"'\":c=bc(a);break;default:if(isNaN(+c))if(!Cb(c)&&/(?![0-9])[\\w]/.test(d)&&\"(\"==y(a.a,1)){c=z(a.a);\nc=Bb[c]||null;z(a.a);for(d=[];\")\"!=y(a.a);){T(a,\"Missing function argument list.\");d.push(Yb(a));if(\",\"!=y(a.a))break;z(a.a)}T(a,\"Unclosed function argument list.\");ac(a);c=new zb(c,d)}else{c=null;break a}else c=new Eb(+z(a.a))}\"[\"==y(a.a)&&(d=new Mb(ec(a)),c=new xb(c,d))}if(c)if(Jb(y(a.a)))d=c;else return c;else c=dc(a,\"/\"),d=new Ib,b.push(c)}for(;Jb(y(a.a));)c=z(a.a),T(a,\"Missing next location step.\"),c=dc(a,c),b.push(c);return new Fb(d,b)}\nfunction dc(a,b){if(\"/\"!=b&&\"//\"!=b)throw Error('Step op should be \"/\" or \"//\"');if(\".\"==y(a.a)){var c=new R(Sb,new G(\"node\"));z(a.a);return c}if(\"..\"==y(a.a))return c=new R(Rb,new G(\"node\")),z(a.a),c;if(\"@\"==y(a.a)){var d=Gb;z(a.a);T(a,\"Missing attribute name\")}else if(\"::\"==y(a.a,1)){if(!/(?![0-9])[\\w]/.test(y(a.a).charAt(0)))throw Error(\"Bad token: \"+z(a.a));var e=z(a.a);d=Qb[e]||null;if(!d)throw Error(\"No axis with name: \"+e);z(a.a);T(a,\"Missing node name\")}else d=Nb;e=y(a.a);if(/(?![0-9])[\\w\\*]/.test(e.charAt(0)))if(\"(\"==\ny(a.a,1)){if(!Cb(e))throw Error(\"Invalid node type: \"+e);e=z(a.a);if(!Cb(e))throw Error(\"Invalid type name: \"+e);$b(a,\"(\");T(a,\"Bad nodetype\");var f=y(a.a).charAt(0),g=null;if('\"'==f||\"'\"==f)g=bc(a);T(a,\"Bad nodetype\");ac(a);e=new G(e,g)}else if(e=z(a.a),f=e.indexOf(\":\"),-1==f)e=new F(e);else{g=e.substring(0,f);if(\"*\"==g)var h=\"*\";else if(h=a.b(g),!h)throw Error(\"Namespace prefix not declared: \"+g);e=e.substr(f+1);e=new F(e,h)}else throw Error(\"Bad token: \"+z(a.a));a=new Mb(ec(a),d.s);return c||new R(d,\ne,a,\"//\"==b)}function ec(a){for(var b=[];\"[\"==y(a.a);){z(a.a);T(a,\"Missing predicate expression.\");var c=Yb(a);b.push(c);T(a,\"Unclosed predicate expression.\");$b(a,\"]\")}return b}function Zb(a){if(\"-\"==y(a.a))return z(a.a),new Tb(Zb(a));var b=cc(a);if(\"|\"!=y(a.a))a=b;else{for(b=[b];\"|\"==z(a.a);)T(a,\"Missing next union location path.\"),b.push(cc(a));a.a.a--;a=new Ub(b)}return a};function fc(a){switch(a.nodeType){case 1:return ha(gc,a);case 9:return fc(a.documentElement);case 11:case 10:case 6:case 12:return hc;default:return a.parentNode?fc(a.parentNode):hc}}function hc(){return null}function gc(a,b){if(a.prefix==b)return a.namespaceURI||\"http://www.w3.org/1999/xhtml\";var c=a.getAttributeNode(\"xmlns:\"+b);return c&&c.specified?c.value||null:a.parentNode&&9!=a.parentNode.nodeType?gc(a.parentNode,b):null};function ic(a,b){if(!a.length)throw Error(\"Empty XPath expression.\");a=Qa(a);if(Ta(a))throw Error(\"Invalid XPath expression.\");b?\"function\"==ca(b)||(b=fa(b.lookupNamespaceURI,b)):b=function(){return null};var c=Yb(new Vb(a,b));if(!Ta(a))throw Error(\"Bad token: \"+z(a));this.evaluate=function(d,e){d=c.a(new ia(d));return new U(d,e)}}\nfunction U(a,b){if(0==b)if(a instanceof E)b=4;else if(\"string\"==typeof a)b=2;else if(\"number\"==typeof a)b=1;else if(\"boolean\"==typeof a)b=3;else throw Error(\"Unexpected evaluation result.\");if(2!=b&&1!=b&&3!=b&&!(a instanceof E))throw Error(\"value could not be converted to the specified type\");this.resultType=b;switch(b){case 2:this.stringValue=a instanceof E?nb(a):\"\"+a;break;case 1:this.numberValue=a instanceof E?+nb(a):+a;break;case 3:this.booleanValue=a instanceof E?0\u003Ca.l:!!a;break;case 4:case 5:case 6:case 7:var c=\nH(a);var d=[];for(var e=I(c);e;e=I(c))d.push(e instanceof Na?e.a:e);this.snapshotLength=a.l;this.invalidIteratorState=!1;break;case 8:case 9:a=mb(a);this.singleNodeValue=a instanceof Na?a.a:a;break;default:throw Error(\"Unknown XPathResult type.\");}var f=0;this.iterateNext=function(){if(4!=b&&5!=b)throw Error(\"iterateNext called with wrong result type\");return f>=d.length?null:d[f++]};this.snapshotItem=function(g){if(6!=b&&7!=b)throw Error(\"snapshotItem called with wrong result type\");return g>=d.length||\n0>g?null:d[g]}}U.ANY_TYPE=0;U.NUMBER_TYPE=1;U.STRING_TYPE=2;U.BOOLEAN_TYPE=3;U.UNORDERED_NODE_ITERATOR_TYPE=4;U.ORDERED_NODE_ITERATOR_TYPE=5;U.UNORDERED_NODE_SNAPSHOT_TYPE=6;U.ORDERED_NODE_SNAPSHOT_TYPE=7;U.ANY_UNORDERED_NODE_TYPE=8;U.FIRST_ORDERED_NODE_TYPE=9;function jc(a){this.lookupNamespaceURI=fc(a)}\nfunction kc(a,b){a=a||k;var c=a.Document&&a.Document.prototype||a.document;if(!c.evaluate||b)a.XPathResult=U,c.evaluate=function(d,e,f,g){return(new ic(d,f)).evaluate(e,g)},c.createExpression=function(d,e){return new ic(d,e)},c.createNSResolver=function(d){return new jc(d)}}ba(\"wgxpath.install\",kc);ba(\"wgxpath.install\",kc);var lc={aliceblue:\"#f0f8ff\",antiquewhite:\"#faebd7\",aqua:\"#00ffff\",aquamarine:\"#7fffd4\",azure:\"#f0ffff\",beige:\"#f5f5dc\",bisque:\"#ffe4c4\",black:\"#000000\",blanchedalmond:\"#ffebcd\",blue:\"#0000ff\",blueviolet:\"#8a2be2\",brown:\"#a52a2a\",burlywood:\"#deb887\",cadetblue:\"#5f9ea0\",chartreuse:\"#7fff00\",chocolate:\"#d2691e\",coral:\"#ff7f50\",cornflowerblue:\"#6495ed\",cornsilk:\"#fff8dc\",crimson:\"#dc143c\",cyan:\"#00ffff\",darkblue:\"#00008b\",darkcyan:\"#008b8b\",darkgoldenrod:\"#b8860b\",darkgray:\"#a9a9a9\",darkgreen:\"#006400\",\ndarkgrey:\"#a9a9a9\",darkkhaki:\"#bdb76b\",darkmagenta:\"#8b008b\",darkolivegreen:\"#556b2f\",darkorange:\"#ff8c00\",darkorchid:\"#9932cc\",darkred:\"#8b0000\",darksalmon:\"#e9967a\",darkseagreen:\"#8fbc8f\",darkslateblue:\"#483d8b\",darkslategray:\"#2f4f4f\",darkslategrey:\"#2f4f4f\",darkturquoise:\"#00ced1\",darkviolet:\"#9400d3\",deeppink:\"#ff1493\",deepskyblue:\"#00bfff\",dimgray:\"#696969\",dimgrey:\"#696969\",dodgerblue:\"#1e90ff\",firebrick:\"#b22222\",floralwhite:\"#fffaf0\",forestgreen:\"#228b22\",fuchsia:\"#ff00ff\",gainsboro:\"#dcdcdc\",\nghostwhite:\"#f8f8ff\",gold:\"#ffd700\",goldenrod:\"#daa520\",gray:\"#808080\",green:\"#008000\",greenyellow:\"#adff2f\",grey:\"#808080\",honeydew:\"#f0fff0\",hotpink:\"#ff69b4\",indianred:\"#cd5c5c\",indigo:\"#4b0082\",ivory:\"#fffff0\",khaki:\"#f0e68c\",lavender:\"#e6e6fa\",lavenderblush:\"#fff0f5\",lawngreen:\"#7cfc00\",lemonchiffon:\"#fffacd\",lightblue:\"#add8e6\",lightcoral:\"#f08080\",lightcyan:\"#e0ffff\",lightgoldenrodyellow:\"#fafad2\",lightgray:\"#d3d3d3\",lightgreen:\"#90ee90\",lightgrey:\"#d3d3d3\",lightpink:\"#ffb6c1\",lightsalmon:\"#ffa07a\",\nlightseagreen:\"#20b2aa\",lightskyblue:\"#87cefa\",lightslategray:\"#778899\",lightslategrey:\"#778899\",lightsteelblue:\"#b0c4de\",lightyellow:\"#ffffe0\",lime:\"#00ff00\",limegreen:\"#32cd32\",linen:\"#faf0e6\",magenta:\"#ff00ff\",maroon:\"#800000\",mediumaquamarine:\"#66cdaa\",mediumblue:\"#0000cd\",mediumorchid:\"#ba55d3\",mediumpurple:\"#9370db\",mediumseagreen:\"#3cb371\",mediumslateblue:\"#7b68ee\",mediumspringgreen:\"#00fa9a\",mediumturquoise:\"#48d1cc\",mediumvioletred:\"#c71585\",midnightblue:\"#191970\",mintcream:\"#f5fffa\",mistyrose:\"#ffe4e1\",\nmoccasin:\"#ffe4b5\",navajowhite:\"#ffdead\",navy:\"#000080\",oldlace:\"#fdf5e6\",olive:\"#808000\",olivedrab:\"#6b8e23\",orange:\"#ffa500\",orangered:\"#ff4500\",orchid:\"#da70d6\",palegoldenrod:\"#eee8aa\",palegreen:\"#98fb98\",paleturquoise:\"#afeeee\",palevioletred:\"#db7093\",papayawhip:\"#ffefd5\",peachpuff:\"#ffdab9\",peru:\"#cd853f\",pink:\"#ffc0cb\",plum:\"#dda0dd\",powderblue:\"#b0e0e6\",purple:\"#800080\",red:\"#ff0000\",rosybrown:\"#bc8f8f\",royalblue:\"#4169e1\",saddlebrown:\"#8b4513\",salmon:\"#fa8072\",sandybrown:\"#f4a460\",seagreen:\"#2e8b57\",\nseashell:\"#fff5ee\",sienna:\"#a0522d\",silver:\"#c0c0c0\",skyblue:\"#87ceeb\",slateblue:\"#6a5acd\",slategray:\"#708090\",slategrey:\"#708090\",snow:\"#fffafa\",springgreen:\"#00ff7f\",steelblue:\"#4682b4\",tan:\"#d2b48c\",teal:\"#008080\",thistle:\"#d8bfd8\",tomato:\"#ff6347\",turquoise:\"#40e0d0\",violet:\"#ee82ee\",wheat:\"#f5deb3\",white:\"#ffffff\",whitesmoke:\"#f5f5f5\",yellow:\"#ffff00\",yellowgreen:\"#9acd32\"};var mc=\"backgroundColor borderTopColor borderRightColor borderBottomColor borderLeftColor color outlineColor\".split(\" \"),nc=/#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])/,oc=/^#(?:[0-9a-f]{3}){1,2}$/i,pc=/^(?:rgba)?\\((\\d{1,3}),\\s?(\\d{1,3}),\\s?(\\d{1,3}),\\s?(0|1|0\\.\\d*)\\)$/i,qc=/^(?:rgb)?\\((0|[1-9]\\d{0,2}),\\s?(0|[1-9]\\d{0,2}),\\s?(0|[1-9]\\d{0,2})\\)$/i;function rc(a,b){this.code=a;this.a=V[a]||sc;this.message=b||\"\";a=this.a.replace(/((?:^|\\s+)[a-z])/g,function(c){return c.toUpperCase().replace(/^[\\s\\xa0]+/g,\"\")});b=a.length-5;if(0>b||a.indexOf(\"Error\",b)!=b)a+=\"Error\";this.name=a;a=Error(this.message);a.name=this.name;this.stack=a.stack||\"\"}l(rc,Error);var sc=\"unknown error\",V={15:\"element not selectable\",11:\"element not visible\"};V[31]=sc;V[30]=sc;V[24]=\"invalid cookie domain\";V[29]=\"invalid element coordinates\";V[12]=\"invalid element state\";\nV[32]=\"invalid selector\";V[51]=\"invalid selector\";V[52]=\"invalid selector\";V[17]=\"javascript error\";V[405]=\"unsupported operation\";V[34]=\"move target out of bounds\";V[27]=\"no such alert\";V[7]=\"no such element\";V[8]=\"no such frame\";V[23]=\"no such window\";V[28]=\"script timeout\";V[33]=\"session not created\";V[10]=\"stale element reference\";V[21]=\"timeout\";V[25]=\"unable to set cookie\";V[26]=\"unexpected alert open\";V[13]=sc;V[9]=\"unknown command\";var tc=va(),uc=ya()||u(\"iPod\"),vc=u(\"iPad\"),wc=u(\"Android\")&&!(wa()||va()||u(\"Opera\")||u(\"Silk\")),xc=wa(),yc=u(\"Safari\")&&!(wa()||u(\"Coast\")||u(\"Opera\")||u(\"Edge\")||u(\"Edg/\")||u(\"OPR\")||va()||u(\"Silk\")||u(\"Android\"))&&!(ya()||u(\"iPad\")||u(\"iPod\"));function zc(a){return(a=a.exec(t))?a[1]:\"\"}(function(){if(tc)return zc(/Firefox\\/([0-9.]+)/);if(v||Ca||Ba)return Ga;if(xc)return ya()||u(\"iPad\")||u(\"iPod\")?zc(/CriOS\\/([0-9.]+)/):zc(/Chrome\\/([0-9.]+)/);if(yc&&!(ya()||u(\"iPad\")||u(\"iPod\")))return zc(/Version\\/([0-9.]+)/);if(uc||vc){var a=/Version\\/(\\S+).*Mobile\\/(\\S+)/.exec(t);if(a)return a[1]+\".\"+a[2]}else if(wc)return(a=zc(/Android\\s+([0-9.]+)/))?a:zc(/Version\\/([0-9.]+)/);return\"\"})();var Ac=v&&!(9\u003C=Number(La));function W(a,b){b&&\"string\"!==typeof b&&(b=b.toString());return!!a&&1==a.nodeType&&(!b||a.tagName.toUpperCase()==b)};var Bc=function(){var a={K:\"http://www.w3.org/2000/svg\"};return function(b){return a[b]||null}}();\nfunction Cc(a,b){var c=A(a);if(!c.documentElement)return null;(v||wc)&&kc(c?c.parentWindow||c.defaultView:window);try{var d=c.createNSResolver?c.createNSResolver(c.documentElement):Bc;if(v&&!Ka(7))return c.evaluate.call(c,b,a,d,9,null);if(!v||9\u003C=Number(La)){for(var e={},f=c.getElementsByTagName(\"*\"),g=0;g\u003Cf.length;++g){var h=f[g],m=h.namespaceURI;if(m&&!e[m]){var w=h.lookupPrefix(m);if(!w){var r=m.match(\".*/(\\\\w+)/?$\");w=r?r[1]:\"xhtml\"}e[m]=w}}var D={},L;for(L in e)D[e[L]]=L;d=function(M){return D[M]||\nnull}}try{return c.evaluate(b,a,d,9,null)}catch(M){if(\"TypeError\"===M.name)return d=c.createNSResolver?c.createNSResolver(c.documentElement):Bc,c.evaluate(b,a,d,9,null);throw M;}}catch(M){if(!Da||\"NS_ERROR_ILLEGAL_VALUE\"!=M.name)throw new rc(32,\"Unable to locate an element with the xpath expression \"+b+\" because of the following error:\\n\"+M);}}\nfunction Dc(a,b){var c=function(){var d=Cc(b,a);return d?d.singleNodeValue||null:b.selectSingleNode?(d=A(b),d.setProperty&&d.setProperty(\"SelectionLanguage\",\"XPath\"),b.selectSingleNode(a)):null}();if(null!==c&&(!c||1!=c.nodeType))throw new rc(32,'The result of the xpath expression \"'+a+'\" is: '+c+\". It should be an element.\");return c};function Ec(a,b,c,d){this.c=a;this.a=b;this.b=c;this.f=d}Ec.prototype.ceil=function(){this.c=Math.ceil(this.c);this.a=Math.ceil(this.a);this.b=Math.ceil(this.b);this.f=Math.ceil(this.f);return this};Ec.prototype.floor=function(){this.c=Math.floor(this.c);this.a=Math.floor(this.a);this.b=Math.floor(this.b);this.f=Math.floor(this.f);return this};Ec.prototype.round=function(){this.c=Math.round(this.c);this.a=Math.round(this.a);this.b=Math.round(this.b);this.f=Math.round(this.f);return this};function X(a,b,c,d){this.a=a;this.b=b;this.width=c;this.height=d}X.prototype.ceil=function(){this.a=Math.ceil(this.a);this.b=Math.ceil(this.b);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};X.prototype.floor=function(){this.a=Math.floor(this.a);this.b=Math.floor(this.b);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};\nX.prototype.round=function(){this.a=Math.round(this.a);this.b=Math.round(this.b);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};var Fc=\"function\"===typeof ShadowRoot;function Gc(a){for(a=a.parentNode;a&&1!=a.nodeType&&9!=a.nodeType&&11!=a.nodeType;)a=a.parentNode;return W(a)?a:null}\nfunction Y(a,b){b=xa(b);if(\"float\"==b||\"cssFloat\"==b||\"styleFloat\"==b)b=Ac?\"styleFloat\":\"cssFloat\";a:{var c=b;var d=A(a);if(d.defaultView&&d.defaultView.getComputedStyle&&(d=d.defaultView.getComputedStyle(a,null))){c=d[c]||d.getPropertyValue(c)||\"\";break a}c=\"\"}a=c||Hc(a,b);if(null===a)a=null;else if(0\u003C=ja(mc,b)){b:{var e=a.match(pc);if(e&&(b=Number(e[1]),c=Number(e[2]),d=Number(e[3]),e=Number(e[4]),0\u003C=b&&255>=b&&0\u003C=c&&255>=c&&0\u003C=d&&255>=d&&0\u003C=e&&1>=e)){b=[b,c,d,e];break b}b=null}if(!b)b:{if(d=a.match(qc))if(b=\nNumber(d[1]),c=Number(d[2]),d=Number(d[3]),0\u003C=b&&255>=b&&0\u003C=c&&255>=c&&0\u003C=d&&255>=d){b=[b,c,d,1];break b}b=null}if(!b)b:{b=a.toLowerCase();c=lc[b.toLowerCase()];if(!c&&(c=\"#\"==b.charAt(0)?b:\"#\"+b,4==c.length&&(c=c.replace(nc,\"#$1$1$2$2$3$3\")),!oc.test(c))){b=null;break b}b=[parseInt(c.substr(1,2),16),parseInt(c.substr(3,2),16),parseInt(c.substr(5,2),16),1]}a=b?\"rgba(\"+b.join(\", \")+\")\":a}return a}\nfunction Hc(a,b){var c=a.currentStyle||a.style,d=c[b];void 0===d&&\"function\"==ca(c.getPropertyValue)&&(d=c.getPropertyValue(b));return\"inherit\"!=d?void 0!==d?d:null:(a=Gc(a))?Hc(a,b):null}\nfunction Ic(a,b,c){function d(g){var h=Jc(g);return 0\u003Ch.height&&0\u003Ch.width?!0:W(g,\"PATH\")&&(0\u003Ch.height||0\u003Ch.width)?(g=Y(g,\"stroke-width\"),!!g&&0\u003CparseInt(g,10)):\"hidden\"!=Y(g,\"overflow\")&&ma(g.childNodes,function(m){return 3==m.nodeType||W(m)&&d(m)})}function e(g){return Kc(g)==Z&&na(g.childNodes,function(h){return!W(h)||e(h)||!d(h)})}if(!W(a))throw Error(\"Argument to isShown must be of type Element\");if(W(a,\"BODY\"))return!0;if(W(a,\"OPTION\")||W(a,\"OPTGROUP\"))return a=$a(a,function(g){return W(g,\"SELECT\")}),\n!!a&&Ic(a,!0,c);var f=Lc(a);if(f)return!!f.image&&0\u003Cf.rect.width&&0\u003Cf.rect.height&&Ic(f.image,b,c);if(W(a,\"INPUT\")&&\"hidden\"==a.type.toLowerCase()||W(a,\"NOSCRIPT\"))return!1;f=Y(a,\"visibility\");return\"collapse\"!=f&&\"hidden\"!=f&&c(a)&&(b||0!=Mc(a))&&d(a)?!e(a):!1}var Z=\"hidden\";\nfunction Kc(a){function b(p){function q(fb){if(fb==g)return!0;var Wb=Y(fb,\"display\");return 0==Wb.lastIndexOf(\"inline\",0)||\"contents\"==Wb||\"absolute\"==Xb&&\"static\"==Y(fb,\"position\")?!1:!0}var Xb=Y(p,\"position\");if(\"fixed\"==Xb)return w=!0,p==g?null:g;for(p=Gc(p);p&&!q(p);)p=Gc(p);return p}function c(p){var q=p;if(\"visible\"==m)if(p==g&&h)q=h;else if(p==h)return{x:\"visible\",y:\"visible\"};q={x:Y(q,\"overflow-x\"),y:Y(q,\"overflow-y\")};p==g&&(q.x=\"visible\"==q.x?\"auto\":q.x,q.y=\"visible\"==q.y?\"auto\":q.y);return q}\nfunction d(p){if(p==g){var q=(new ab(f)).a;p=q.scrollingElement?q.scrollingElement:Ea||\"CSS1Compat\"!=q.compatMode?q.body||q.documentElement:q.documentElement;q=q.parentWindow||q.defaultView;p=v&&Ka(\"10\")&&q.pageYOffset!=p.scrollTop?new Ua(p.scrollLeft,p.scrollTop):new Ua(q.pageXOffset||p.scrollLeft,q.pageYOffset||p.scrollTop)}else p=new Ua(p.scrollLeft,p.scrollTop);return p}var e=Nc(a),f=A(a),g=f.documentElement,h=f.body,m=Y(g,\"overflow\"),w;for(a=b(a);a;a=b(a)){var r=c(a);if(\"visible\"!=r.x||\"visible\"!=\nr.y){var D=Jc(a);if(0==D.width||0==D.height)return Z;var L=e.a\u003CD.a,M=e.b\u003CD.b;if(L&&\"hidden\"==r.x||M&&\"hidden\"==r.y)return Z;if(L&&\"visible\"!=r.x||M&&\"visible\"!=r.y){L=d(a);M=e.b\u003CD.b-L.y;if(e.a\u003CD.a-L.x&&\"visible\"!=r.x||M&&\"visible\"!=r.x)return Z;e=Kc(a);return e==Z?Z:\"scroll\"}L=e.f>=D.a+D.width;D=e.c>=D.b+D.height;if(L&&\"hidden\"==r.x||D&&\"hidden\"==r.y)return Z;if(L&&\"visible\"!=r.x||D&&\"visible\"!=r.y){if(w&&(r=d(a),e.f>=g.scrollWidth-r.x||e.a>=g.scrollHeight-r.y))return Z;e=Kc(a);return e==Z?Z:\"scroll\"}}}return\"none\"}\nfunction Jc(a){var b=Lc(a);if(b)return b.rect;if(W(a,\"HTML\"))return a=A(a),a=((a?a.parentWindow||a.defaultView:window)||window).document,a=\"CSS1Compat\"==a.compatMode?a.documentElement:a.body,a=new Va(a.clientWidth,a.clientHeight),new X(0,0,a.width,a.height);try{var c=a.getBoundingClientRect()}catch(d){return new X(0,0,0,0)}b=new X(c.left,c.top,c.right-c.left,c.bottom-c.top);v&&a.ownerDocument.body&&(a=A(a),b.a-=a.documentElement.clientLeft+a.body.clientLeft,b.b-=a.documentElement.clientTop+a.body.clientTop);\nreturn b}function Lc(a){var b=W(a,\"MAP\");if(!b&&!W(a,\"AREA\"))return null;var c=b?a:W(a.parentNode,\"MAP\")?a.parentNode:null,d=null,e=null;c&&c.name&&(d=Dc('/descendant::*[@usemap = \"#'+c.name+'\"]',A(c)))&&(e=Jc(d),b||\"default\"==a.shape.toLowerCase()||(a=Oc(a),b=Math.min(Math.max(a.a,0),e.width),c=Math.min(Math.max(a.b,0),e.height),e=new X(b+e.a,c+e.b,Math.min(a.width,e.width-b),Math.min(a.height,e.height-c))));return{image:d,rect:e||new X(0,0,0,0)}}\nfunction Oc(a){var b=a.shape.toLowerCase();a=a.coords.split(\",\");if(\"rect\"==b&&4==a.length){b=a[0];var c=a[1];return new X(b,c,a[2]-b,a[3]-c)}if(\"circle\"==b&&3==a.length)return b=a[2],new X(a[0]-b,a[1]-b,2*b,2*b);if(\"poly\"==b&&2\u003Ca.length){b=a[0];c=a[1];for(var d=b,e=c,f=2;f+1\u003Ca.length;f+=2)b=Math.min(b,a[f]),d=Math.max(d,a[f]),c=Math.min(c,a[f+1]),e=Math.max(e,a[f+1]);return new X(b,c,d-b,e-c)}return new X(0,0,0,0)}function Nc(a){a=Jc(a);return new Ec(a.b,a.a+a.width,a.b+a.height,a.a)}\nfunction Mc(a){if(Ac){if(\"relative\"==Y(a,\"position\"))return 1;a=Y(a,\"filter\");return(a=a.match(/^alpha\\(opacity=(\\d*)\\)/)||a.match(/^progid:DXImageTransform.Microsoft.Alpha\\(Opacity=(\\d*)\\)/))?Number(a[1])/100:1}return Pc(a)}function Pc(a){var b=1,c=Y(a,\"opacity\");c&&(b=Number(c));(a=Gc(a))&&(b*=Pc(a));return b};ba(\"_\",function(a,b){function c(d){if(W(d)&&\"none\"==Y(d,\"display\"))return!1;var e;if((e=d.parentNode)&&e.shadowRoot&&void 0!==d.assignedSlot)e=d.assignedSlot?d.assignedSlot.parentNode:null;else if(d.getDestinationInsertionPoints){var f=d.getDestinationInsertionPoints();0\u003Cf.length&&(e=f[f.length-1])}if(Fc&&e instanceof ShadowRoot){if(e.host.shadowRoot!==e)return!1;e=e.host}return!e||9!=e.nodeType&&11!=e.nodeType?e&&W(e,\"DETAILS\")&&!e.open&&!W(d,\"SUMMARY\")?!1:!!e&&c(e):!0}return Ic(a,!!b,c)});; return this._.apply(null,arguments);}).apply({navigator:typeof window!='undefined'?window.navigator:null,document:typeof window!='undefined'?window.document:null}, arguments);}\n).apply(null, arguments);"
}
[**********.121][INFO]: Waiting for pending navigations...
[**********.121][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=109) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.122][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=109) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.122][INFO]: Done waiting for pending navigations. Status: ok
[**********.125][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=110) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.144][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=110) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.144][INFO]: Waiting for pending navigations...
[**********.144][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=111) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.147][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=111) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.147][INFO]: Done waiting for pending navigations. Status: ok
[**********.147][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ExecuteScript true
[**********.149][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND IsElementEnabled {
   "id": "6d5ff0be-83e4-41c9-af82-60b7d4b7d38b"
}
[**********.149][INFO]: Waiting for pending navigations...
[**********.149][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=112) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.150][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=112) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.150][INFO]: Done waiting for pending navigations. Status: ok
[**********.151][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=113) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.contentType",
   "returnByValue": true
}
[**********.152][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=113) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "text/html"
   }
}
[**********.153][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=114) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.161][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=114) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.161][INFO]: Waiting for pending navigations...
[**********.161][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=115) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.162][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=115) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.162][INFO]: Done waiting for pending navigations. Status: ok
[**********.162][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE IsElementEnabled true
[**********.165][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ClickElement {
   "id": "6d5ff0be-83e4-41c9-af82-60b7d4b7d38b"
}
[**********.165][INFO]: Waiting for pending navigations...
[**********.165][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=116) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.166][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=116) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.166][INFO]: Done waiting for pending navigations. Status: ok
[**********.167][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=117) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.186][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=117) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "button"
      }
   }
}
[**********.186][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=118) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.210][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=118) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "button"
      }
   }
}
[**********.212][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=119) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.229][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=119) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.230][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=120) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.234][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=120) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "height": 36,
            "left": 0,
            "top": 0,
            "width": 300
         }
      }
   }
}
[**********.236][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=121) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.249][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=121) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "ceil": {
            },
            "clone": {
            },
            "floor": {
            },
            "round": {
            },
            "scale": {
            },
            "translate": {
            },
            "x": 802,
            "y": 548
         }
      }
   }
}
[**********.251][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=122) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.260][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=122) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "clickable": true,
            "message": "Element's descendant would receive the click. Consider clicking the descendant instead. Descendant: \u003Cspan class=\"css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3\">...\u003C/span>"
         }
      }
   }
}
[**********.260][DEBUG]: DevTools WebSocket Command: Input.dispatchMouseEvent (id=123) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "button": "none",
   "buttons": 0,
   "clickCount": 0,
   "force": 0.0,
   "modifiers": 0,
   "pointerType": "mouse",
   "tangentialPressure": 0.0,
   "tiltX": 0,
   "tiltY": 0,
   "twist": 0,
   "type": "mouseMoved",
   "x": 952,
   "y": 566
}
[**********.260][DEBUG]: DevTools WebSocket Command: Input.dispatchMouseEvent (id=124) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "button": "left",
   "buttons": 0,
   "clickCount": 1,
   "force": 0.0,
   "modifiers": 0,
   "pointerType": "mouse",
   "tangentialPressure": 0.0,
   "tiltX": 0,
   "tiltY": 0,
   "twist": 0,
   "type": "mousePressed",
   "x": 952,
   "y": 566
}
[**********.260][DEBUG]: DevTools WebSocket Command: Input.dispatchMouseEvent (id=125) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "button": "left",
   "buttons": 1,
   "clickCount": 1,
   "force": 0.0,
   "modifiers": 0,
   "pointerType": "mouse",
   "tangentialPressure": 0.0,
   "tiltX": 0,
   "tiltY": 0,
   "twist": 0,
   "type": "mouseReleased",
   "x": 952,
   "y": 566
}
[**********.265][DEBUG]: DevTools WebSocket Response: Input.dispatchMouseEvent (id=123) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.275][DEBUG]: DevTools WebSocket Response: Input.dispatchMouseEvent (id=124) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.291][DEBUG]: DevTools WebSocket Event: Page.frameDetached (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "C59C5943E8F65A2AB5DB15BC3EFAB160",
   "reason": "remove"
}
[**********.291][DEBUG]: DevTools WebSocket Event: Target.detachedFromTarget (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "sessionId": "A1D623DFAA3B849582F758C682AC198F",
   "targetId": "C59C5943E8F65A2AB5DB15BC3EFAB160"
}
[**********.488][DEBUG]: DevTools WebSocket Response: Input.dispatchMouseEvent (id=125) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.488][INFO]: Waiting for pending navigations...
[**********.488][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=126) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.502][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=126) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.503][INFO]: Done waiting for pending navigations. Status: ok
[**********.503][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ClickElement
[1749127462.341][DEBUG]: DevTools WebSocket Event: Log.entryAdded (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "entry": {
      "args": [ {
         "className": "HTMLInputElement",
         "description": "input.r-30o5oe.r-1dz5y72.r-13qz1uu.r-1niwhzg.r-17gur6a.r-1yadl64.r-deolkf.r-homxoj.r-poiln3.r-7cikom.r-1ny4l3l.r-t60dpp.r-fdjqy7",
         "objectId": "4409707110441190648.1.2",
         "subtype": "node",
         "type": "object"
      } ],
      "level": "verbose",
      "source": "recommendation",
      "text": "[DOM] Password field is not contained in a form: (More info: https://goo.gl/9p2vKq) %o",
      "timestamp": 1.749127462339266e+12,
      "url": "https://x.com/i/flow/login"
   }
}
[**********.508][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND FindElement {
   "using": "xpath",
   "value": "//input[@autocomplete=\"current-password\"]"
}
[**********.508][INFO]: Waiting for pending navigations...
[**********.508][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=127) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.510][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=127) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.510][INFO]: Done waiting for pending navigations. Status: ok
[**********.512][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=128) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.529][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=128) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "element-6066-11e4-a52e-4f735466cecf": "121210af-b71b-4906-826b-ed1500f3df09"
         }
      }
   }
}
[**********.529][INFO]: Waiting for pending navigations...
[**********.529][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=129) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.530][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=129) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.530][INFO]: Done waiting for pending navigations. Status: ok
[**********.530][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE FindElement {
   "element-6066-11e4-a52e-4f735466cecf": "121210af-b71b-4906-826b-ed1500f3df09"
}
[**********.534][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ClearElement {
   "id": "121210af-b71b-4906-826b-ed1500f3df09"
}
[**********.534][INFO]: Waiting for pending navigations...
[**********.534][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=130) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.535][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=130) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.536][INFO]: Done waiting for pending navigations. Status: ok
[**********.536][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=131) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.540][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=131) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "input"
      }
   }
}
[**********.541][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=132) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.552][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=132) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "password"
      }
   }
}
[**********.553][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=133) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.556][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=133) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": false
      }
   }
}
[**********.558][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=134) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.573][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=134) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.576][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=135) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.594][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=135) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": null
      }
   }
}
[**********.594][INFO]: Waiting for pending navigations...
[**********.594][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=136) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.595][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=136) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.595][INFO]: Done waiting for pending navigations. Status: ok
[**********.596][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ClearElement
[**********.599][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND TypeElement {
   "id": "121210af-b71b-4906-826b-ed1500f3df09",
   "text": "dreamlucid11201120!!",
   "value": [ "d", "r", "e", "a", "m", "l", "u", "c", "i", "d", "1", "1", "2", "0", "1", "1", "2", "0", "!", "!" ]
}
[**********.599][INFO]: Waiting for pending navigations...
[**********.599][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=137) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.600][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=137) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.600][INFO]: Done waiting for pending navigations. Status: ok
[**********.601][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=138) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.611][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=138) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "INPUT"
      }
   }
}
[**********.613][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=139) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.617][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=139) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "password"
      }
   }
}
[**********.617][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=140) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.620][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=140) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": false
      }
   }
}
[**********.622][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=141) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.627][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=141) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": "INPUT"
      }
   }
}
[**********.627][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=142) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.hasFocus()",
   "returnByValue": true
}
[**********.628][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=142) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "boolean",
      "value": true
   }
}
[**********.629][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=143) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.631][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=143) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "element-6066-11e4-a52e-4f735466cecf": "121210af-b71b-4906-826b-ed1500f3df09"
         }
      }
   }
}
[**********.631][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=144) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyD",
   "key": "d",
   "modifiers": 0,
   "text": "d",
   "type": "rawKeyDown",
   "unmodifiedText": "d",
   "windowsVirtualKeyCode": 68
}
[**********.631][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=145) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyD",
   "key": "d",
   "modifiers": 0,
   "text": "d",
   "type": "char",
   "unmodifiedText": "d",
   "windowsVirtualKeyCode": 68
}
[**********.631][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=146) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyD",
   "key": "d",
   "modifiers": 0,
   "text": "d",
   "type": "keyUp",
   "unmodifiedText": "d",
   "windowsVirtualKeyCode": 68
}
[**********.631][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=147) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyR",
   "key": "r",
   "modifiers": 0,
   "text": "r",
   "type": "rawKeyDown",
   "unmodifiedText": "r",
   "windowsVirtualKeyCode": 82
}
[**********.632][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=148) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyR",
   "key": "r",
   "modifiers": 0,
   "text": "r",
   "type": "char",
   "unmodifiedText": "r",
   "windowsVirtualKeyCode": 82
}
[**********.632][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=149) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyR",
   "key": "r",
   "modifiers": 0,
   "text": "r",
   "type": "keyUp",
   "unmodifiedText": "r",
   "windowsVirtualKeyCode": 82
}
[**********.632][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=150) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyE",
   "key": "e",
   "modifiers": 0,
   "text": "e",
   "type": "rawKeyDown",
   "unmodifiedText": "e",
   "windowsVirtualKeyCode": 69
}
[**********.632][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=151) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyE",
   "key": "e",
   "modifiers": 0,
   "text": "e",
   "type": "char",
   "unmodifiedText": "e",
   "windowsVirtualKeyCode": 69
}
[**********.632][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=152) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyE",
   "key": "e",
   "modifiers": 0,
   "text": "e",
   "type": "keyUp",
   "unmodifiedText": "e",
   "windowsVirtualKeyCode": 69
}
[**********.632][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=153) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyA",
   "key": "a",
   "modifiers": 0,
   "text": "a",
   "type": "rawKeyDown",
   "unmodifiedText": "a",
   "windowsVirtualKeyCode": 65
}
[**********.632][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=154) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyA",
   "key": "a",
   "modifiers": 0,
   "text": "a",
   "type": "char",
   "unmodifiedText": "a",
   "windowsVirtualKeyCode": 65
}
[**********.633][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=155) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyA",
   "key": "a",
   "modifiers": 0,
   "text": "a",
   "type": "keyUp",
   "unmodifiedText": "a",
   "windowsVirtualKeyCode": 65
}
[**********.633][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=156) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyM",
   "key": "m",
   "modifiers": 0,
   "text": "m",
   "type": "rawKeyDown",
   "unmodifiedText": "m",
   "windowsVirtualKeyCode": 77
}
[**********.633][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=157) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyM",
   "key": "m",
   "modifiers": 0,
   "text": "m",
   "type": "char",
   "unmodifiedText": "m",
   "windowsVirtualKeyCode": 77
}
[**********.633][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=158) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyM",
   "key": "m",
   "modifiers": 0,
   "text": "m",
   "type": "keyUp",
   "unmodifiedText": "m",
   "windowsVirtualKeyCode": 77
}
[**********.633][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=159) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyL",
   "key": "l",
   "modifiers": 0,
   "text": "l",
   "type": "rawKeyDown",
   "unmodifiedText": "l",
   "windowsVirtualKeyCode": 76
}
[**********.633][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=160) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyL",
   "key": "l",
   "modifiers": 0,
   "text": "l",
   "type": "char",
   "unmodifiedText": "l",
   "windowsVirtualKeyCode": 76
}
[**********.633][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=161) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyL",
   "key": "l",
   "modifiers": 0,
   "text": "l",
   "type": "keyUp",
   "unmodifiedText": "l",
   "windowsVirtualKeyCode": 76
}
[**********.634][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=162) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyU",
   "key": "u",
   "modifiers": 0,
   "text": "u",
   "type": "rawKeyDown",
   "unmodifiedText": "u",
   "windowsVirtualKeyCode": 85
}
[**********.634][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=163) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyU",
   "key": "u",
   "modifiers": 0,
   "text": "u",
   "type": "char",
   "unmodifiedText": "u",
   "windowsVirtualKeyCode": 85
}
[**********.634][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=164) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyU",
   "key": "u",
   "modifiers": 0,
   "text": "u",
   "type": "keyUp",
   "unmodifiedText": "u",
   "windowsVirtualKeyCode": 85
}
[**********.634][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=165) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyC",
   "key": "c",
   "modifiers": 0,
   "text": "c",
   "type": "rawKeyDown",
   "unmodifiedText": "c",
   "windowsVirtualKeyCode": 67
}
[**********.634][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=166) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyC",
   "key": "c",
   "modifiers": 0,
   "text": "c",
   "type": "char",
   "unmodifiedText": "c",
   "windowsVirtualKeyCode": 67
}
[**********.634][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=167) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyC",
   "key": "c",
   "modifiers": 0,
   "text": "c",
   "type": "keyUp",
   "unmodifiedText": "c",
   "windowsVirtualKeyCode": 67
}
[**********.635][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=168) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyI",
   "key": "i",
   "modifiers": 0,
   "text": "i",
   "type": "rawKeyDown",
   "unmodifiedText": "i",
   "windowsVirtualKeyCode": 73
}
[**********.635][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=169) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyI",
   "key": "i",
   "modifiers": 0,
   "text": "i",
   "type": "char",
   "unmodifiedText": "i",
   "windowsVirtualKeyCode": 73
}
[**********.635][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=170) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyI",
   "key": "i",
   "modifiers": 0,
   "text": "i",
   "type": "keyUp",
   "unmodifiedText": "i",
   "windowsVirtualKeyCode": 73
}
[**********.635][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=171) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyD",
   "key": "d",
   "modifiers": 0,
   "text": "d",
   "type": "rawKeyDown",
   "unmodifiedText": "d",
   "windowsVirtualKeyCode": 68
}
[**********.635][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=172) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyD",
   "key": "d",
   "modifiers": 0,
   "text": "d",
   "type": "char",
   "unmodifiedText": "d",
   "windowsVirtualKeyCode": 68
}
[**********.635][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=173) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "KeyD",
   "key": "d",
   "modifiers": 0,
   "text": "d",
   "type": "keyUp",
   "unmodifiedText": "d",
   "windowsVirtualKeyCode": 68
}
[**********.635][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=174) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "rawKeyDown",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.636][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=175) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "char",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.636][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=176) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "keyUp",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.636][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=177) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "rawKeyDown",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.636][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=178) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "char",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.636][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=179) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "keyUp",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.636][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=180) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit2",
   "key": "2",
   "modifiers": 0,
   "text": "2",
   "type": "rawKeyDown",
   "unmodifiedText": "2",
   "windowsVirtualKeyCode": 50
}
[**********.637][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=181) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit2",
   "key": "2",
   "modifiers": 0,
   "text": "2",
   "type": "char",
   "unmodifiedText": "2",
   "windowsVirtualKeyCode": 50
}
[**********.637][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=182) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit2",
   "key": "2",
   "modifiers": 0,
   "text": "2",
   "type": "keyUp",
   "unmodifiedText": "2",
   "windowsVirtualKeyCode": 50
}
[**********.637][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=183) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit0",
   "key": "0",
   "modifiers": 0,
   "text": "0",
   "type": "rawKeyDown",
   "unmodifiedText": "0",
   "windowsVirtualKeyCode": 48
}
[**********.637][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=184) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit0",
   "key": "0",
   "modifiers": 0,
   "text": "0",
   "type": "char",
   "unmodifiedText": "0",
   "windowsVirtualKeyCode": 48
}
[**********.637][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=185) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit0",
   "key": "0",
   "modifiers": 0,
   "text": "0",
   "type": "keyUp",
   "unmodifiedText": "0",
   "windowsVirtualKeyCode": 48
}
[**********.637][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=186) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "rawKeyDown",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.637][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=187) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "char",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.638][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=188) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "keyUp",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.638][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=189) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "rawKeyDown",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.638][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=190) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "char",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.638][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=191) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "1",
   "modifiers": 0,
   "text": "1",
   "type": "keyUp",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.638][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=192) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit2",
   "key": "2",
   "modifiers": 0,
   "text": "2",
   "type": "rawKeyDown",
   "unmodifiedText": "2",
   "windowsVirtualKeyCode": 50
}
[**********.638][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=193) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit2",
   "key": "2",
   "modifiers": 0,
   "text": "2",
   "type": "char",
   "unmodifiedText": "2",
   "windowsVirtualKeyCode": 50
}
[**********.638][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=194) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit2",
   "key": "2",
   "modifiers": 0,
   "text": "2",
   "type": "keyUp",
   "unmodifiedText": "2",
   "windowsVirtualKeyCode": 50
}
[**********.639][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=195) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit0",
   "key": "0",
   "modifiers": 0,
   "text": "0",
   "type": "rawKeyDown",
   "unmodifiedText": "0",
   "windowsVirtualKeyCode": 48
}
[**********.639][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=196) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit0",
   "key": "0",
   "modifiers": 0,
   "text": "0",
   "type": "char",
   "unmodifiedText": "0",
   "windowsVirtualKeyCode": 48
}
[**********.639][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=197) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit0",
   "key": "0",
   "modifiers": 0,
   "text": "0",
   "type": "keyUp",
   "unmodifiedText": "0",
   "windowsVirtualKeyCode": 48
}
[**********.639][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=198) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "ShiftLeft",
   "key": "Shift",
   "modifiers": 0,
   "text": "",
   "type": "rawKeyDown",
   "unmodifiedText": "",
   "windowsVirtualKeyCode": 16
}
[**********.639][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=199) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "!",
   "modifiers": 8,
   "text": "!",
   "type": "rawKeyDown",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.639][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=200) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "!",
   "modifiers": 8,
   "text": "!",
   "type": "char",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.639][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=201) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "!",
   "modifiers": 8,
   "text": "!",
   "type": "keyUp",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.640][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=202) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "ShiftLeft",
   "key": "Shift",
   "modifiers": 0,
   "text": "",
   "type": "keyUp",
   "unmodifiedText": "",
   "windowsVirtualKeyCode": 16
}
[**********.640][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=203) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "ShiftLeft",
   "key": "Shift",
   "modifiers": 0,
   "text": "",
   "type": "rawKeyDown",
   "unmodifiedText": "",
   "windowsVirtualKeyCode": 16
}
[**********.640][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=204) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "!",
   "modifiers": 8,
   "text": "!",
   "type": "rawKeyDown",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.640][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=205) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "!",
   "modifiers": 8,
   "text": "!",
   "type": "char",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.640][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=206) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "Digit1",
   "key": "!",
   "modifiers": 8,
   "text": "!",
   "type": "keyUp",
   "unmodifiedText": "1",
   "windowsVirtualKeyCode": 49
}
[**********.640][DEBUG]: DevTools WebSocket Command: Input.dispatchKeyEvent (id=207) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "code": "ShiftLeft",
   "key": "Shift",
   "modifiers": 0,
   "text": "",
   "type": "keyUp",
   "unmodifiedText": "",
   "windowsVirtualKeyCode": 16
}
[**********.641][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=144) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.663][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=145) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.664][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=146) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.664][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=147) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.683][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=148) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.683][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=149) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.684][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=150) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.703][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=151) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.703][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=152) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.704][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=153) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.722][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=154) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.722][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=155) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.723][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=156) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.741][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=157) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.741][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=158) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.741][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=159) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.760][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=160) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.761][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=161) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.761][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=162) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.783][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=163) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.784][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=164) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.784][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=165) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.802][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=166) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.802][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=167) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.803][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=168) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.821][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=169) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.821][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=170) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.822][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=171) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.844][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=172) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.845][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=173) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.845][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=174) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.863][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=175) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.863][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=176) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.863][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=177) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.881][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=178) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.881][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=179) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.882][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=180) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.900][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=181) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.900][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=182) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.901][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=183) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.919][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=184) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.919][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=185) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.919][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=186) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.938][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=187) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.938][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=188) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.939][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=189) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.955][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=190) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.955][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=191) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.956][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=192) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.976][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=193) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.977][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=194) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.977][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=195) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.996][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=196) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.996][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=197) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.997][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=198) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[**********.997][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=199) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.014][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=200) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.014][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=201) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.014][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=202) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.015][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=203) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.016][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=204) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.033][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=205) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.033][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=206) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.034][DEBUG]: DevTools WebSocket Response: Input.dispatchKeyEvent (id=207) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127467.034][INFO]: Waiting for pending navigations...
[1749127467.034][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=208) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[1749127467.041][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=208) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[1749127467.041][INFO]: Done waiting for pending navigations. Status: ok
[1749127467.041][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE TypeElement
[**********.045][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND FindElement {
   "using": "xpath",
   "value": "//button[@data-testid='LoginForm_Login_Button']"
}
[**********.045][INFO]: Waiting for pending navigations...
[**********.045][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=209) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.047][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=209) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.047][INFO]: Done waiting for pending navigations. Status: ok
[**********.049][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=210) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.064][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=210) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": {
            "element-6066-11e4-a52e-4f735466cecf": "ad91d644-1af6-4960-a246-81b5872f40b8"
         }
      }
   }
}
[**********.064][INFO]: Waiting for pending navigations...
[**********.064][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=211) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.065][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=211) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.065][INFO]: Done waiting for pending navigations. Status: ok
[**********.065][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE FindElement {
   "element-6066-11e4-a52e-4f735466cecf": "ad91d644-1af6-4960-a246-81b5872f40b8"
}
[**********.071][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ExecuteScript {
   "args": [ {
      "element-6066-11e4-a52e-4f735466cecf": "ad91d644-1af6-4960-a246-81b5872f40b8"
   } ],
   "script": "/* isDisplayed */return (function(){return (function(){var k=this||self;function aa(a){return\"string\"==typeof a}function ba(a,b){a=a.split(\".\");var c=k;a[0]in c||\"undefined\"==typeof c.execScript||c.execScript(\"var \"+a[0]);for(var d;a.length&&(d=a.shift());)a.length||void 0===b?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b}\nfunction ca(a){var b=typeof a;if(\"object\"==b)if(a){if(a instanceof Array)return\"array\";if(a instanceof Object)return b;var c=Object.prototype.toString.call(a);if(\"[object Window]\"==c)return\"object\";if(\"[object Array]\"==c||\"number\"==typeof a.length&&\"undefined\"!=typeof a.splice&&\"undefined\"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable(\"splice\"))return\"array\";if(\"[object Function]\"==c||\"undefined\"!=typeof a.call&&\"undefined\"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable(\"call\"))return\"function\"}else return\"null\";\nelse if(\"function\"==b&&\"undefined\"==typeof a.call)return\"object\";return b}function da(a,b,c){return a.call.apply(a.bind,arguments)}function ea(a,b,c){if(!a)throw Error();if(2\u003Carguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}\nfunction fa(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf(\"native code\")?fa=da:fa=ea;return fa.apply(null,arguments)}function ha(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function l(a,b){function c(){}c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};/*\n\n The MIT License\n\n Copyright (c) 2007 Cybozu Labs, Inc.\n Copyright (c) 2012 Google Inc.\n\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to\n deal in the Software without restriction, including without limitation the\n rights to use, copy, modify, merge, publish, distribute, sublicense, and/or\n sell copies of the Software, and to permit persons to whom the Software is\n furnished to do so, subject to the following conditions:\n\n The above copyright notice and this permission notice shall be included in\n all copies or substantial portions of the Software.\n\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n IN THE SOFTWARE.\n*/\nfunction ia(a,b,c){this.a=a;this.b=b||1;this.f=c||1};var ja=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(\"string\"===typeof a)return\"string\"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c\u003Ca.length;c++)if(c in a&&a[c]===b)return c;return-1},n=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=\"string\"===typeof a?a.split(\"\"):a,e=0;e\u003Cc;e++)e in d&&b.call(void 0,d[e],e,a)},ka=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,\nb,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f=\"string\"===typeof a?a.split(\"\"):a,g=0;g\u003Cc;g++)if(g in f){var h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d},la=Array.prototype.reduce?function(a,b,c){return Array.prototype.reduce.call(a,b,c)}:function(a,b,c){var d=c;n(a,function(e,f){d=b.call(void 0,d,e,f,a)});return d},ma=Array.prototype.some?function(a,b){return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=\"string\"===typeof a?a.split(\"\"):a,e=0;e\u003Cc;e++)if(e in\nd&&b.call(void 0,d[e],e,a))return!0;return!1},na=Array.prototype.every?function(a,b){return Array.prototype.every.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=\"string\"===typeof a?a.split(\"\"):a,e=0;e\u003Cc;e++)if(e in d&&!b.call(void 0,d[e],e,a))return!1;return!0};function oa(a,b){a:{for(var c=a.length,d=\"string\"===typeof a?a.split(\"\"):a,e=0;e\u003Cc;e++)if(e in d&&b.call(void 0,d[e],e,a)){b=e;break a}b=-1}return 0>b?null:\"string\"===typeof a?a.charAt(b):a[b]}\nfunction pa(a){return Array.prototype.concat.apply([],arguments)}function qa(a,b,c){return 2>=arguments.length?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)};var ra=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\\s\\xa0]*([\\s\\S]*?)[\\s\\xa0]*$/.exec(a)[1]};function sa(a,b){return a\u003Cb?-1:a>b?1:0};var t;a:{var ta=k.navigator;if(ta){var ua=ta.userAgent;if(ua){t=ua;break a}}t=\"\"}function u(a){return-1!=t.indexOf(a)};function va(){return u(\"Firefox\")||u(\"FxiOS\")}function wa(){return(u(\"Chrome\")||u(\"CriOS\"))&&!u(\"Edge\")};function xa(a){return String(a).replace(/\\-([a-z])/g,function(b,c){return c.toUpperCase()})};function ya(){return u(\"iPhone\")&&!u(\"iPod\")&&!u(\"iPad\")};function za(a,b){var c=Aa;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};var Ba=u(\"Opera\"),v=u(\"Trident\")||u(\"MSIE\"),Ca=u(\"Edge\"),Da=u(\"Gecko\")&&!(-1!=t.toLowerCase().indexOf(\"webkit\")&&!u(\"Edge\"))&&!(u(\"Trident\")||u(\"MSIE\"))&&!u(\"Edge\"),Ea=-1!=t.toLowerCase().indexOf(\"webkit\")&&!u(\"Edge\");function Fa(){var a=k.document;return a?a.documentMode:void 0}var Ga;\na:{var Ha=\"\",Ia=function(){var a=t;if(Da)return/rv:([^\\);]+)(\\)|;)/.exec(a);if(Ca)return/Edge\\/([\\d\\.]+)/.exec(a);if(v)return/\\b(?:MSIE|rv)[: ]([^\\);]+)(\\)|;)/.exec(a);if(Ea)return/WebKit\\/(\\S+)/.exec(a);if(Ba)return/(?:Version)[ \\/]?(\\S+)/.exec(a)}();Ia&&(Ha=Ia?Ia[1]:\"\");if(v){var Ja=Fa();if(null!=Ja&&Ja>parseFloat(Ha)){Ga=String(Ja);break a}}Ga=Ha}var Aa={};\nfunction Ka(a){return za(a,function(){for(var b=0,c=ra(String(Ga)).split(\".\"),d=ra(String(a)).split(\".\"),e=Math.max(c.length,d.length),f=0;0==b&&f\u003Ce;f++){var g=c[f]||\"\",h=d[f]||\"\";do{g=/(\\d*)(\\D*)(.*)/.exec(g)||[\"\",\"\",\"\",\"\"];h=/(\\d*)(\\D*)(.*)/.exec(h)||[\"\",\"\",\"\",\"\"];if(0==g[0].length&&0==h[0].length)break;b=sa(0==g[1].length?0:parseInt(g[1],10),0==h[1].length?0:parseInt(h[1],10))||sa(0==g[2].length,0==h[2].length)||sa(g[2],h[2]);g=g[3];h=h[3]}while(0==b)}return 0\u003C=b})}var La;\nLa=k.document&&v?Fa():void 0;var x=v&&!(9\u003C=Number(La)),Ma=v&&!(8\u003C=Number(La));function Na(a,b,c,d){this.a=a;this.nodeName=c;this.nodeValue=d;this.nodeType=2;this.parentNode=this.ownerElement=b}function Oa(a,b){var c=Ma&&\"href\"==b.nodeName?a.getAttribute(b.nodeName,2):b.nodeValue;return new Na(b,a,b.nodeName,c)};function Pa(a){this.b=a;this.a=0}function Qa(a){a=a.match(Ra);for(var b=0;b\u003Ca.length;b++)Sa.test(a[b])&&a.splice(b,1);return new Pa(a)}var Ra=/\\$?(?:(?![0-9-\\.])(?:\\*|[\\w-\\.]+):)?(?![0-9-\\.])(?:\\*|[\\w-\\.]+)|\\/\\/|\\.\\.|::|\\d+(?:\\.\\d*)?|\\.\\d+|\"[^\"]*\"|'[^']*'|[!\u003C>]=|\\s+|./g,Sa=/^\\s/;function y(a,b){return a.b[a.a+(b||0)]}function z(a){return a.b[a.a++]}function Ta(a){return a.b.length\u003C=a.a};function Ua(a,b){this.x=void 0!==a?a:0;this.y=void 0!==b?b:0}Ua.prototype.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};Ua.prototype.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};Ua.prototype.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};function Va(a,b){this.width=a;this.height=b}Va.prototype.aspectRatio=function(){return this.width/this.height};Va.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};Va.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};Va.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};function Wa(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if(\"undefined\"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a}\nfunction Xa(a,b){if(a==b)return 0;if(a.compareDocumentPosition)return a.compareDocumentPosition(b)&2?1:-1;if(v&&!(9\u003C=Number(La))){if(9==a.nodeType)return-1;if(9==b.nodeType)return 1}if(\"sourceIndex\"in a||a.parentNode&&\"sourceIndex\"in a.parentNode){var c=1==a.nodeType,d=1==b.nodeType;if(c&&d)return a.sourceIndex-b.sourceIndex;var e=a.parentNode,f=b.parentNode;return e==f?Ya(a,b):!c&&Wa(e,b)?-1*Za(a,b):!d&&Wa(f,a)?Za(b,a):(c?a.sourceIndex:e.sourceIndex)-(d?b.sourceIndex:f.sourceIndex)}d=A(a);c=d.createRange();\nc.selectNode(a);c.collapse(!0);a=d.createRange();a.selectNode(b);a.collapse(!0);return c.compareBoundaryPoints(k.Range.START_TO_END,a)}function Za(a,b){var c=a.parentNode;if(c==b)return-1;for(;b.parentNode!=c;)b=b.parentNode;return Ya(b,a)}function Ya(a,b){for(;b=b.previousSibling;)if(b==a)return-1;return 1}function A(a){return 9==a.nodeType?a:a.ownerDocument||a.document}function $a(a,b){a&&(a=a.parentNode);for(var c=0;a;){if(b(a))return a;a=a.parentNode;c++}return null}\nfunction ab(a){this.a=a||k.document||document}ab.prototype.getElementsByTagName=function(a,b){return(b||this.a).getElementsByTagName(String(a))};function B(a){var b=null,c=a.nodeType;1==c&&(b=a.textContent,b=void 0==b||null==b?a.innerText:b,b=void 0==b||null==b?\"\":b);if(\"string\"!=typeof b)if(x&&\"title\"==a.nodeName.toLowerCase()&&1==c)b=a.text;else if(9==c||1==c){a=9==c?a.documentElement:a.firstChild;c=0;var d=[];for(b=\"\";a;){do 1!=a.nodeType&&(b+=a.nodeValue),x&&\"title\"==a.nodeName.toLowerCase()&&(b+=a.text),d[c++]=a;while(a=a.firstChild);for(;c&&!(a=d[--c].nextSibling););}}else b=a.nodeValue;return b}\nfunction C(a,b,c){if(null===b)return!0;try{if(!a.getAttribute)return!1}catch(d){return!1}Ma&&\"class\"==b&&(b=\"className\");return null==c?!!a.getAttribute(b):a.getAttribute(b,2)==c}function bb(a,b,c,d,e){return(x?cb:db).call(null,a,b,aa(c)?c:null,aa(d)?d:null,e||new E)}\nfunction cb(a,b,c,d,e){if(a instanceof F||8==a.b||c&&null===a.b){var f=b.all;if(!f)return e;a=eb(a);if(\"*\"!=a&&(f=b.getElementsByTagName(a),!f))return e;if(c){for(var g=[],h=0;b=f[h++];)C(b,c,d)&&g.push(b);f=g}for(h=0;b=f[h++];)\"*\"==a&&\"!\"==b.tagName||e.add(b);return e}gb(a,b,c,d,e);return e}\nfunction db(a,b,c,d,e){b.getElementsByName&&d&&\"name\"==c&&!v?(b=b.getElementsByName(d),n(b,function(f){a.a(f)&&e.add(f)})):b.getElementsByClassName&&d&&\"class\"==c?(b=b.getElementsByClassName(d),n(b,function(f){f.className==d&&a.a(f)&&e.add(f)})):a instanceof G?gb(a,b,c,d,e):b.getElementsByTagName&&(b=b.getElementsByTagName(a.f()),n(b,function(f){C(f,c,d)&&e.add(f)}));return e}\nfunction hb(a,b,c,d,e){var f;if((a instanceof F||8==a.b||c&&null===a.b)&&(f=b.childNodes)){var g=eb(a);if(\"*\"!=g&&(f=ka(f,function(h){return h.tagName&&h.tagName.toLowerCase()==g}),!f))return e;c&&(f=ka(f,function(h){return C(h,c,d)}));n(f,function(h){\"*\"==g&&(\"!\"==h.tagName||\"*\"==g&&1!=h.nodeType)||e.add(h)});return e}return ib(a,b,c,d,e)}function ib(a,b,c,d,e){for(b=b.firstChild;b;b=b.nextSibling)C(b,c,d)&&a.a(b)&&e.add(b);return e}\nfunction gb(a,b,c,d,e){for(b=b.firstChild;b;b=b.nextSibling)C(b,c,d)&&a.a(b)&&e.add(b),gb(a,b,c,d,e)}function eb(a){if(a instanceof G){if(8==a.b)return\"!\";if(null===a.b)return\"*\"}return a.f()};function E(){this.b=this.a=null;this.l=0}function jb(a){this.f=a;this.a=this.b=null}function kb(a,b){if(!a.a)return b;if(!b.a)return a;var c=a.a;b=b.a;for(var d=null,e,f=0;c&&b;){e=c.f;var g=b.f;e==g||e instanceof Na&&g instanceof Na&&e.a==g.a?(e=c,c=c.a,b=b.a):0\u003CXa(c.f,b.f)?(e=b,b=b.a):(e=c,c=c.a);(e.b=d)?d.a=e:a.a=e;d=e;f++}for(e=c||b;e;)e.b=d,d=d.a=e,f++,e=e.a;a.b=d;a.l=f;return a}function lb(a,b){b=new jb(b);b.a=a.a;a.b?a.a.b=b:a.a=a.b=b;a.a=b;a.l++}\nE.prototype.add=function(a){a=new jb(a);a.b=this.b;this.a?this.b.a=a:this.a=this.b=a;this.b=a;this.l++};function mb(a){return(a=a.a)?a.f:null}function nb(a){return(a=mb(a))?B(a):\"\"}function H(a,b){return new ob(a,!!b)}function ob(a,b){this.f=a;this.b=(this.s=b)?a.b:a.a;this.a=null}function I(a){var b=a.b;if(null==b)return null;var c=a.a=b;a.b=a.s?b.b:b.a;return c.f};function J(a){this.i=a;this.b=this.g=!1;this.f=null}function K(a){return\"\\n  \"+a.toString().split(\"\\n\").join(\"\\n  \")}function pb(a,b){a.g=b}function qb(a,b){a.b=b}function N(a,b){a=a.a(b);return a instanceof E?+nb(a):+a}function O(a,b){a=a.a(b);return a instanceof E?nb(a):\"\"+a}function rb(a,b){a=a.a(b);return a instanceof E?!!a.l:!!a};function sb(a,b,c){J.call(this,a.i);this.c=a;this.h=b;this.o=c;this.g=b.g||c.g;this.b=b.b||c.b;this.c==tb&&(c.b||c.g||4==c.i||0==c.i||!b.f?b.b||b.g||4==b.i||0==b.i||!c.f||(this.f={name:c.f.name,u:b}):this.f={name:b.f.name,u:c})}l(sb,J);\nfunction ub(a,b,c,d,e){b=b.a(d);c=c.a(d);var f;if(b instanceof E&&c instanceof E){b=H(b);for(d=I(b);d;d=I(b))for(e=H(c),f=I(e);f;f=I(e))if(a(B(d),B(f)))return!0;return!1}if(b instanceof E||c instanceof E){b instanceof E?(e=b,d=c):(e=c,d=b);f=H(e);for(var g=typeof d,h=I(f);h;h=I(f)){switch(g){case \"number\":h=+B(h);break;case \"boolean\":h=!!B(h);break;case \"string\":h=B(h);break;default:throw Error(\"Illegal primitive type for comparison.\");}if(e==b&&a(h,d)||e==c&&a(d,h))return!0}return!1}return e?\"boolean\"==\ntypeof b||\"boolean\"==typeof c?a(!!b,!!c):\"number\"==typeof b||\"number\"==typeof c?a(+b,+c):a(b,c):a(+b,+c)}sb.prototype.a=function(a){return this.c.m(this.h,this.o,a)};sb.prototype.toString=function(){var a=\"Binary Expression: \"+this.c;a+=K(this.h);return a+=K(this.o)};function vb(a,b,c,d){this.I=a;this.D=b;this.i=c;this.m=d}vb.prototype.toString=function(){return this.I};var wb={};\nfunction P(a,b,c,d){if(wb.hasOwnProperty(a))throw Error(\"Binary operator already created: \"+a);a=new vb(a,b,c,d);return wb[a.toString()]=a}P(\"div\",6,1,function(a,b,c){return N(a,c)/N(b,c)});P(\"mod\",6,1,function(a,b,c){return N(a,c)%N(b,c)});P(\"*\",6,1,function(a,b,c){return N(a,c)*N(b,c)});P(\"+\",5,1,function(a,b,c){return N(a,c)+N(b,c)});P(\"-\",5,1,function(a,b,c){return N(a,c)-N(b,c)});P(\"\u003C\",4,2,function(a,b,c){return ub(function(d,e){return d\u003Ce},a,b,c)});\nP(\">\",4,2,function(a,b,c){return ub(function(d,e){return d>e},a,b,c)});P(\"\u003C=\",4,2,function(a,b,c){return ub(function(d,e){return d\u003C=e},a,b,c)});P(\">=\",4,2,function(a,b,c){return ub(function(d,e){return d>=e},a,b,c)});var tb=P(\"=\",3,2,function(a,b,c){return ub(function(d,e){return d==e},a,b,c,!0)});P(\"!=\",3,2,function(a,b,c){return ub(function(d,e){return d!=e},a,b,c,!0)});P(\"and\",2,2,function(a,b,c){return rb(a,c)&&rb(b,c)});P(\"or\",1,2,function(a,b,c){return rb(a,c)||rb(b,c)});function xb(a,b){if(b.a.length&&4!=a.i)throw Error(\"Primary expression must evaluate to nodeset if filter has predicate(s).\");J.call(this,a.i);this.c=a;this.h=b;this.g=a.g;this.b=a.b}l(xb,J);xb.prototype.a=function(a){a=this.c.a(a);return yb(this.h,a)};xb.prototype.toString=function(){var a=\"Filter:\"+K(this.c);return a+=K(this.h)};function zb(a,b){if(b.length\u003Ca.C)throw Error(\"Function \"+a.j+\" expects at least\"+a.C+\" arguments, \"+b.length+\" given\");if(null!==a.B&&b.length>a.B)throw Error(\"Function \"+a.j+\" expects at most \"+a.B+\" arguments, \"+b.length+\" given\");a.H&&n(b,function(c,d){if(4!=c.i)throw Error(\"Argument \"+d+\" to function \"+a.j+\" is not of type Nodeset: \"+c);});J.call(this,a.i);this.v=a;this.c=b;pb(this,a.g||ma(b,function(c){return c.g}));qb(this,a.G&&!b.length||a.F&&!!b.length||ma(b,function(c){return c.b}))}\nl(zb,J);zb.prototype.a=function(a){return this.v.m.apply(null,pa(a,this.c))};zb.prototype.toString=function(){var a=\"Function: \"+this.v;if(this.c.length){var b=la(this.c,function(c,d){return c+K(d)},\"Arguments:\");a+=K(b)}return a};function Ab(a,b,c,d,e,f,g,h){this.j=a;this.i=b;this.g=c;this.G=d;this.F=!1;this.m=e;this.C=f;this.B=void 0!==g?g:f;this.H=!!h}Ab.prototype.toString=function(){return this.j};var Bb={};\nfunction Q(a,b,c,d,e,f,g,h){if(Bb.hasOwnProperty(a))throw Error(\"Function already created: \"+a+\".\");Bb[a]=new Ab(a,b,c,d,e,f,g,h)}Q(\"boolean\",2,!1,!1,function(a,b){return rb(b,a)},1);Q(\"ceiling\",1,!1,!1,function(a,b){return Math.ceil(N(b,a))},1);Q(\"concat\",3,!1,!1,function(a,b){return la(qa(arguments,1),function(c,d){return c+O(d,a)},\"\")},2,null);Q(\"contains\",2,!1,!1,function(a,b,c){b=O(b,a);a=O(c,a);return-1!=b.indexOf(a)},2);Q(\"count\",1,!1,!1,function(a,b){return b.a(a).l},1,1,!0);\nQ(\"false\",2,!1,!1,function(){return!1},0);Q(\"floor\",1,!1,!1,function(a,b){return Math.floor(N(b,a))},1);Q(\"id\",4,!1,!1,function(a,b){function c(h){if(x){var m=e.all[h];if(m){if(m.nodeType&&h==m.id)return m;if(m.length)return oa(m,function(w){return h==w.id})}return null}return e.getElementById(h)}var d=a.a,e=9==d.nodeType?d:d.ownerDocument;a=O(b,a).split(/\\s+/);var f=[];n(a,function(h){h=c(h);!h||0\u003C=ja(f,h)||f.push(h)});f.sort(Xa);var g=new E;n(f,function(h){g.add(h)});return g},1);\nQ(\"lang\",2,!1,!1,function(){return!1},1);Q(\"last\",1,!0,!1,function(a){if(1!=arguments.length)throw Error(\"Function last expects ()\");return a.f},0);Q(\"local-name\",3,!1,!0,function(a,b){return(a=b?mb(b.a(a)):a.a)?a.localName||a.nodeName.toLowerCase():\"\"},0,1,!0);Q(\"name\",3,!1,!0,function(a,b){return(a=b?mb(b.a(a)):a.a)?a.nodeName.toLowerCase():\"\"},0,1,!0);Q(\"namespace-uri\",3,!0,!1,function(){return\"\"},0,1,!0);\nQ(\"normalize-space\",3,!1,!0,function(a,b){return(b?O(b,a):B(a.a)).replace(/[\\s\\xa0]+/g,\" \").replace(/^\\s+|\\s+$/g,\"\")},0,1);Q(\"not\",2,!1,!1,function(a,b){return!rb(b,a)},1);Q(\"number\",1,!1,!0,function(a,b){return b?N(b,a):+B(a.a)},0,1);Q(\"position\",1,!0,!1,function(a){return a.b},0);Q(\"round\",1,!1,!1,function(a,b){return Math.round(N(b,a))},1);Q(\"starts-with\",2,!1,!1,function(a,b,c){b=O(b,a);a=O(c,a);return 0==b.lastIndexOf(a,0)},2);Q(\"string\",3,!1,!0,function(a,b){return b?O(b,a):B(a.a)},0,1);\nQ(\"string-length\",1,!1,!0,function(a,b){return(b?O(b,a):B(a.a)).length},0,1);Q(\"substring\",3,!1,!1,function(a,b,c,d){c=N(c,a);if(isNaN(c)||Infinity==c||-Infinity==c)return\"\";d=d?N(d,a):Infinity;if(isNaN(d)||-Infinity===d)return\"\";c=Math.round(c)-1;var e=Math.max(c,0);a=O(b,a);return Infinity==d?a.substring(e):a.substring(e,c+Math.round(d))},2,3);Q(\"substring-after\",3,!1,!1,function(a,b,c){b=O(b,a);a=O(c,a);c=b.indexOf(a);return-1==c?\"\":b.substring(c+a.length)},2);\nQ(\"substring-before\",3,!1,!1,function(a,b,c){b=O(b,a);a=O(c,a);a=b.indexOf(a);return-1==a?\"\":b.substring(0,a)},2);Q(\"sum\",1,!1,!1,function(a,b){a=H(b.a(a));b=0;for(var c=I(a);c;c=I(a))b+=+B(c);return b},1,1,!0);Q(\"translate\",3,!1,!1,function(a,b,c,d){b=O(b,a);c=O(c,a);var e=O(d,a);a={};for(d=0;d\u003Cc.length;d++){var f=c.charAt(d);f in a||(a[f]=e.charAt(d))}c=\"\";for(d=0;d\u003Cb.length;d++)f=b.charAt(d),c+=f in a?a[f]:f;return c},3);Q(\"true\",2,!1,!1,function(){return!0},0);function G(a,b){this.h=a;this.c=void 0!==b?b:null;this.b=null;switch(a){case \"comment\":this.b=8;break;case \"text\":this.b=3;break;case \"processing-instruction\":this.b=7;break;case \"node\":break;default:throw Error(\"Unexpected argument\");}}function Cb(a){return\"comment\"==a||\"text\"==a||\"processing-instruction\"==a||\"node\"==a}G.prototype.a=function(a){return null===this.b||this.b==a.nodeType};G.prototype.f=function(){return this.h};\nG.prototype.toString=function(){var a=\"Kind Test: \"+this.h;null===this.c||(a+=K(this.c));return a};function Db(a){J.call(this,3);this.c=a.substring(1,a.length-1)}l(Db,J);Db.prototype.a=function(){return this.c};Db.prototype.toString=function(){return\"Literal: \"+this.c};function F(a,b){this.j=a.toLowerCase();a=\"*\"==this.j?\"*\":\"http://www.w3.org/1999/xhtml\";this.c=b?b.toLowerCase():a}F.prototype.a=function(a){var b=a.nodeType;if(1!=b&&2!=b)return!1;b=void 0!==a.localName?a.localName:a.nodeName;return\"*\"!=this.j&&this.j!=b.toLowerCase()?!1:\"*\"==this.c?!0:this.c==(a.namespaceURI?a.namespaceURI.toLowerCase():\"http://www.w3.org/1999/xhtml\")};F.prototype.f=function(){return this.j};\nF.prototype.toString=function(){return\"Name Test: \"+(\"http://www.w3.org/1999/xhtml\"==this.c?\"\":this.c+\":\")+this.j};function Eb(a){J.call(this,1);this.c=a}l(Eb,J);Eb.prototype.a=function(){return this.c};Eb.prototype.toString=function(){return\"Number: \"+this.c};function Fb(a,b){J.call(this,a.i);this.h=a;this.c=b;this.g=a.g;this.b=a.b;1==this.c.length&&(a=this.c[0],a.A||a.c!=Gb||(a=a.o,\"*\"!=a.f()&&(this.f={name:a.f(),u:null})))}l(Fb,J);function Hb(){J.call(this,4)}l(Hb,J);Hb.prototype.a=function(a){var b=new E;a=a.a;9==a.nodeType?b.add(a):b.add(a.ownerDocument);return b};Hb.prototype.toString=function(){return\"Root Helper Expression\"};function Ib(){J.call(this,4)}l(Ib,J);Ib.prototype.a=function(a){var b=new E;b.add(a.a);return b};Ib.prototype.toString=function(){return\"Context Helper Expression\"};\nfunction Jb(a){return\"/\"==a||\"//\"==a}Fb.prototype.a=function(a){var b=this.h.a(a);if(!(b instanceof E))throw Error(\"Filter expression must evaluate to nodeset.\");a=this.c;for(var c=0,d=a.length;c\u003Cd&&b.l;c++){var e=a[c],f=H(b,e.c.s);if(e.g||e.c!=Kb)if(e.g||e.c!=Lb){var g=I(f);for(b=e.a(new ia(g));null!=(g=I(f));)g=e.a(new ia(g)),b=kb(b,g)}else g=I(f),b=e.a(new ia(g));else{for(g=I(f);(b=I(f))&&(!g.contains||g.contains(b))&&b.compareDocumentPosition(g)&8;g=b);b=e.a(new ia(g))}}return b};\nFb.prototype.toString=function(){var a=\"Path Expression:\"+K(this.h);if(this.c.length){var b=la(this.c,function(c,d){return c+K(d)},\"Steps:\");a+=K(b)}return a};function Mb(a,b){this.a=a;this.s=!!b}\nfunction yb(a,b,c){for(c=c||0;c\u003Ca.a.length;c++)for(var d=a.a[c],e=H(b),f=b.l,g,h=0;g=I(e);h++){var m=a.s?f-h:h+1;g=d.a(new ia(g,m,f));if(\"number\"==typeof g)m=m==g;else if(\"string\"==typeof g||\"boolean\"==typeof g)m=!!g;else if(g instanceof E)m=0\u003Cg.l;else throw Error(\"Predicate.evaluate returned an unexpected type.\");if(!m){m=e;g=m.f;var w=m.a;if(!w)throw Error(\"Next must be called at least once before remove.\");var r=w.b;w=w.a;r?r.a=w:g.a=w;w?w.b=r:g.b=r;g.l--;m.a=null}}return b}\nMb.prototype.toString=function(){return la(this.a,function(a,b){return a+K(b)},\"Predicates:\")};function R(a,b,c,d){J.call(this,4);this.c=a;this.o=b;this.h=c||new Mb([]);this.A=!!d;b=this.h;b=0\u003Cb.a.length?b.a[0].f:null;a.J&&b&&(a=b.name,a=x?a.toLowerCase():a,this.f={name:a,u:b.u});a:{a=this.h;for(b=0;b\u003Ca.a.length;b++)if(c=a.a[b],c.g||1==c.i||0==c.i){a=!0;break a}a=!1}this.g=a}l(R,J);\nR.prototype.a=function(a){var b=a.a,c=this.f,d=null,e=null,f=0;c&&(d=c.name,e=c.u?O(c.u,a):null,f=1);if(this.A)if(this.g||this.c!=Nb)if(b=H((new R(Ob,new G(\"node\"))).a(a)),c=I(b))for(a=this.m(c,d,e,f);null!=(c=I(b));)a=kb(a,this.m(c,d,e,f));else a=new E;else a=bb(this.o,b,d,e),a=yb(this.h,a,f);else a=this.m(a.a,d,e,f);return a};R.prototype.m=function(a,b,c,d){a=this.c.v(this.o,a,b,c);return a=yb(this.h,a,d)};\nR.prototype.toString=function(){var a=\"Step:\"+K(\"Operator: \"+(this.A?\"//\":\"/\"));this.c.j&&(a+=K(\"Axis: \"+this.c));a+=K(this.o);if(this.h.a.length){var b=la(this.h.a,function(c,d){return c+K(d)},\"Predicates:\");a+=K(b)}return a};function Pb(a,b,c,d){this.j=a;this.v=b;this.s=c;this.J=d}Pb.prototype.toString=function(){return this.j};var Qb={};function S(a,b,c,d){if(Qb.hasOwnProperty(a))throw Error(\"Axis already created: \"+a);b=new Pb(a,b,c,!!d);return Qb[a]=b}\nS(\"ancestor\",function(a,b){for(var c=new E;b=b.parentNode;)a.a(b)&&lb(c,b);return c},!0);S(\"ancestor-or-self\",function(a,b){var c=new E;do a.a(b)&&lb(c,b);while(b=b.parentNode);return c},!0);\nvar Gb=S(\"attribute\",function(a,b){var c=new E,d=a.f();if(\"style\"==d&&x&&b.style)return c.add(new Na(b.style,b,\"style\",b.style.cssText)),c;var e=b.attributes;if(e)if(a instanceof G&&null===a.b||\"*\"==d)for(a=0;d=e[a];a++)x?d.nodeValue&&c.add(Oa(b,d)):c.add(d);else(d=e.getNamedItem(d))&&(x?d.nodeValue&&c.add(Oa(b,d)):c.add(d));return c},!1),Nb=S(\"child\",function(a,b,c,d,e){return(x?hb:ib).call(null,a,b,aa(c)?c:null,aa(d)?d:null,e||new E)},!1,!0);S(\"descendant\",bb,!1,!0);\nvar Ob=S(\"descendant-or-self\",function(a,b,c,d){var e=new E;C(b,c,d)&&a.a(b)&&e.add(b);return bb(a,b,c,d,e)},!1,!0),Kb=S(\"following\",function(a,b,c,d){var e=new E;do for(var f=b;f=f.nextSibling;)C(f,c,d)&&a.a(f)&&e.add(f),e=bb(a,f,c,d,e);while(b=b.parentNode);return e},!1,!0);S(\"following-sibling\",function(a,b){for(var c=new E;b=b.nextSibling;)a.a(b)&&c.add(b);return c},!1);S(\"namespace\",function(){return new E},!1);\nvar Rb=S(\"parent\",function(a,b){var c=new E;if(9==b.nodeType)return c;if(2==b.nodeType)return c.add(b.ownerElement),c;b=b.parentNode;a.a(b)&&c.add(b);return c},!1),Lb=S(\"preceding\",function(a,b,c,d){var e=new E,f=[];do f.unshift(b);while(b=b.parentNode);for(var g=1,h=f.length;g\u003Ch;g++){var m=[];for(b=f[g];b=b.previousSibling;)m.unshift(b);for(var w=0,r=m.length;w\u003Cr;w++)b=m[w],C(b,c,d)&&a.a(b)&&e.add(b),e=bb(a,b,c,d,e)}return e},!0,!0);\nS(\"preceding-sibling\",function(a,b){for(var c=new E;b=b.previousSibling;)a.a(b)&&lb(c,b);return c},!0);var Sb=S(\"self\",function(a,b){var c=new E;a.a(b)&&c.add(b);return c},!1);function Tb(a){J.call(this,1);this.c=a;this.g=a.g;this.b=a.b}l(Tb,J);Tb.prototype.a=function(a){return-N(this.c,a)};Tb.prototype.toString=function(){return\"Unary Expression: -\"+K(this.c)};function Ub(a){J.call(this,4);this.c=a;pb(this,ma(this.c,function(b){return b.g}));qb(this,ma(this.c,function(b){return b.b}))}l(Ub,J);Ub.prototype.a=function(a){var b=new E;n(this.c,function(c){c=c.a(a);if(!(c instanceof E))throw Error(\"Path expression must evaluate to NodeSet.\");b=kb(b,c)});return b};Ub.prototype.toString=function(){return la(this.c,function(a,b){return a+K(b)},\"Union Expression:\")};function Vb(a,b){this.a=a;this.b=b}function Yb(a){for(var b,c=[];;){T(a,\"Missing right hand side of binary expression.\");b=Zb(a);var d=z(a.a);if(!d)break;var e=(d=wb[d]||null)&&d.D;if(!e){a.a.a--;break}for(;c.length&&e\u003C=c[c.length-1].D;)b=new sb(c.pop(),c.pop(),b);c.push(b,d)}for(;c.length;)b=new sb(c.pop(),c.pop(),b);return b}function T(a,b){if(Ta(a.a))throw Error(b);}function $b(a,b){a=z(a.a);if(a!=b)throw Error(\"Bad token, expected: \"+b+\" got: \"+a);}\nfunction ac(a){a=z(a.a);if(\")\"!=a)throw Error(\"Bad token: \"+a);}function bc(a){a=z(a.a);if(2>a.length)throw Error(\"Unclosed literal string\");return new Db(a)}\nfunction cc(a){var b=[];if(Jb(y(a.a))){var c=z(a.a);var d=y(a.a);if(\"/\"==c&&(Ta(a.a)||\".\"!=d&&\"..\"!=d&&\"@\"!=d&&\"*\"!=d&&!/(?![0-9])[\\w]/.test(d)))return new Hb;d=new Hb;T(a,\"Missing next location step.\");c=dc(a,c);b.push(c)}else{a:{c=y(a.a);d=c.charAt(0);switch(d){case \"$\":throw Error(\"Variable reference not allowed in HTML XPath\");case \"(\":z(a.a);c=Yb(a);T(a,'unclosed \"(\"');$b(a,\")\");break;case '\"':case \"'\":c=bc(a);break;default:if(isNaN(+c))if(!Cb(c)&&/(?![0-9])[\\w]/.test(d)&&\"(\"==y(a.a,1)){c=z(a.a);\nc=Bb[c]||null;z(a.a);for(d=[];\")\"!=y(a.a);){T(a,\"Missing function argument list.\");d.push(Yb(a));if(\",\"!=y(a.a))break;z(a.a)}T(a,\"Unclosed function argument list.\");ac(a);c=new zb(c,d)}else{c=null;break a}else c=new Eb(+z(a.a))}\"[\"==y(a.a)&&(d=new Mb(ec(a)),c=new xb(c,d))}if(c)if(Jb(y(a.a)))d=c;else return c;else c=dc(a,\"/\"),d=new Ib,b.push(c)}for(;Jb(y(a.a));)c=z(a.a),T(a,\"Missing next location step.\"),c=dc(a,c),b.push(c);return new Fb(d,b)}\nfunction dc(a,b){if(\"/\"!=b&&\"//\"!=b)throw Error('Step op should be \"/\" or \"//\"');if(\".\"==y(a.a)){var c=new R(Sb,new G(\"node\"));z(a.a);return c}if(\"..\"==y(a.a))return c=new R(Rb,new G(\"node\")),z(a.a),c;if(\"@\"==y(a.a)){var d=Gb;z(a.a);T(a,\"Missing attribute name\")}else if(\"::\"==y(a.a,1)){if(!/(?![0-9])[\\w]/.test(y(a.a).charAt(0)))throw Error(\"Bad token: \"+z(a.a));var e=z(a.a);d=Qb[e]||null;if(!d)throw Error(\"No axis with name: \"+e);z(a.a);T(a,\"Missing node name\")}else d=Nb;e=y(a.a);if(/(?![0-9])[\\w\\*]/.test(e.charAt(0)))if(\"(\"==\ny(a.a,1)){if(!Cb(e))throw Error(\"Invalid node type: \"+e);e=z(a.a);if(!Cb(e))throw Error(\"Invalid type name: \"+e);$b(a,\"(\");T(a,\"Bad nodetype\");var f=y(a.a).charAt(0),g=null;if('\"'==f||\"'\"==f)g=bc(a);T(a,\"Bad nodetype\");ac(a);e=new G(e,g)}else if(e=z(a.a),f=e.indexOf(\":\"),-1==f)e=new F(e);else{g=e.substring(0,f);if(\"*\"==g)var h=\"*\";else if(h=a.b(g),!h)throw Error(\"Namespace prefix not declared: \"+g);e=e.substr(f+1);e=new F(e,h)}else throw Error(\"Bad token: \"+z(a.a));a=new Mb(ec(a),d.s);return c||new R(d,\ne,a,\"//\"==b)}function ec(a){for(var b=[];\"[\"==y(a.a);){z(a.a);T(a,\"Missing predicate expression.\");var c=Yb(a);b.push(c);T(a,\"Unclosed predicate expression.\");$b(a,\"]\")}return b}function Zb(a){if(\"-\"==y(a.a))return z(a.a),new Tb(Zb(a));var b=cc(a);if(\"|\"!=y(a.a))a=b;else{for(b=[b];\"|\"==z(a.a);)T(a,\"Missing next union location path.\"),b.push(cc(a));a.a.a--;a=new Ub(b)}return a};function fc(a){switch(a.nodeType){case 1:return ha(gc,a);case 9:return fc(a.documentElement);case 11:case 10:case 6:case 12:return hc;default:return a.parentNode?fc(a.parentNode):hc}}function hc(){return null}function gc(a,b){if(a.prefix==b)return a.namespaceURI||\"http://www.w3.org/1999/xhtml\";var c=a.getAttributeNode(\"xmlns:\"+b);return c&&c.specified?c.value||null:a.parentNode&&9!=a.parentNode.nodeType?gc(a.parentNode,b):null};function ic(a,b){if(!a.length)throw Error(\"Empty XPath expression.\");a=Qa(a);if(Ta(a))throw Error(\"Invalid XPath expression.\");b?\"function\"==ca(b)||(b=fa(b.lookupNamespaceURI,b)):b=function(){return null};var c=Yb(new Vb(a,b));if(!Ta(a))throw Error(\"Bad token: \"+z(a));this.evaluate=function(d,e){d=c.a(new ia(d));return new U(d,e)}}\nfunction U(a,b){if(0==b)if(a instanceof E)b=4;else if(\"string\"==typeof a)b=2;else if(\"number\"==typeof a)b=1;else if(\"boolean\"==typeof a)b=3;else throw Error(\"Unexpected evaluation result.\");if(2!=b&&1!=b&&3!=b&&!(a instanceof E))throw Error(\"value could not be converted to the specified type\");this.resultType=b;switch(b){case 2:this.stringValue=a instanceof E?nb(a):\"\"+a;break;case 1:this.numberValue=a instanceof E?+nb(a):+a;break;case 3:this.booleanValue=a instanceof E?0\u003Ca.l:!!a;break;case 4:case 5:case 6:case 7:var c=\nH(a);var d=[];for(var e=I(c);e;e=I(c))d.push(e instanceof Na?e.a:e);this.snapshotLength=a.l;this.invalidIteratorState=!1;break;case 8:case 9:a=mb(a);this.singleNodeValue=a instanceof Na?a.a:a;break;default:throw Error(\"Unknown XPathResult type.\");}var f=0;this.iterateNext=function(){if(4!=b&&5!=b)throw Error(\"iterateNext called with wrong result type\");return f>=d.length?null:d[f++]};this.snapshotItem=function(g){if(6!=b&&7!=b)throw Error(\"snapshotItem called with wrong result type\");return g>=d.length||\n0>g?null:d[g]}}U.ANY_TYPE=0;U.NUMBER_TYPE=1;U.STRING_TYPE=2;U.BOOLEAN_TYPE=3;U.UNORDERED_NODE_ITERATOR_TYPE=4;U.ORDERED_NODE_ITERATOR_TYPE=5;U.UNORDERED_NODE_SNAPSHOT_TYPE=6;U.ORDERED_NODE_SNAPSHOT_TYPE=7;U.ANY_UNORDERED_NODE_TYPE=8;U.FIRST_ORDERED_NODE_TYPE=9;function jc(a){this.lookupNamespaceURI=fc(a)}\nfunction kc(a,b){a=a||k;var c=a.Document&&a.Document.prototype||a.document;if(!c.evaluate||b)a.XPathResult=U,c.evaluate=function(d,e,f,g){return(new ic(d,f)).evaluate(e,g)},c.createExpression=function(d,e){return new ic(d,e)},c.createNSResolver=function(d){return new jc(d)}}ba(\"wgxpath.install\",kc);ba(\"wgxpath.install\",kc);var lc={aliceblue:\"#f0f8ff\",antiquewhite:\"#faebd7\",aqua:\"#00ffff\",aquamarine:\"#7fffd4\",azure:\"#f0ffff\",beige:\"#f5f5dc\",bisque:\"#ffe4c4\",black:\"#000000\",blanchedalmond:\"#ffebcd\",blue:\"#0000ff\",blueviolet:\"#8a2be2\",brown:\"#a52a2a\",burlywood:\"#deb887\",cadetblue:\"#5f9ea0\",chartreuse:\"#7fff00\",chocolate:\"#d2691e\",coral:\"#ff7f50\",cornflowerblue:\"#6495ed\",cornsilk:\"#fff8dc\",crimson:\"#dc143c\",cyan:\"#00ffff\",darkblue:\"#00008b\",darkcyan:\"#008b8b\",darkgoldenrod:\"#b8860b\",darkgray:\"#a9a9a9\",darkgreen:\"#006400\",\ndarkgrey:\"#a9a9a9\",darkkhaki:\"#bdb76b\",darkmagenta:\"#8b008b\",darkolivegreen:\"#556b2f\",darkorange:\"#ff8c00\",darkorchid:\"#9932cc\",darkred:\"#8b0000\",darksalmon:\"#e9967a\",darkseagreen:\"#8fbc8f\",darkslateblue:\"#483d8b\",darkslategray:\"#2f4f4f\",darkslategrey:\"#2f4f4f\",darkturquoise:\"#00ced1\",darkviolet:\"#9400d3\",deeppink:\"#ff1493\",deepskyblue:\"#00bfff\",dimgray:\"#696969\",dimgrey:\"#696969\",dodgerblue:\"#1e90ff\",firebrick:\"#b22222\",floralwhite:\"#fffaf0\",forestgreen:\"#228b22\",fuchsia:\"#ff00ff\",gainsboro:\"#dcdcdc\",\nghostwhite:\"#f8f8ff\",gold:\"#ffd700\",goldenrod:\"#daa520\",gray:\"#808080\",green:\"#008000\",greenyellow:\"#adff2f\",grey:\"#808080\",honeydew:\"#f0fff0\",hotpink:\"#ff69b4\",indianred:\"#cd5c5c\",indigo:\"#4b0082\",ivory:\"#fffff0\",khaki:\"#f0e68c\",lavender:\"#e6e6fa\",lavenderblush:\"#fff0f5\",lawngreen:\"#7cfc00\",lemonchiffon:\"#fffacd\",lightblue:\"#add8e6\",lightcoral:\"#f08080\",lightcyan:\"#e0ffff\",lightgoldenrodyellow:\"#fafad2\",lightgray:\"#d3d3d3\",lightgreen:\"#90ee90\",lightgrey:\"#d3d3d3\",lightpink:\"#ffb6c1\",lightsalmon:\"#ffa07a\",\nlightseagreen:\"#20b2aa\",lightskyblue:\"#87cefa\",lightslategray:\"#778899\",lightslategrey:\"#778899\",lightsteelblue:\"#b0c4de\",lightyellow:\"#ffffe0\",lime:\"#00ff00\",limegreen:\"#32cd32\",linen:\"#faf0e6\",magenta:\"#ff00ff\",maroon:\"#800000\",mediumaquamarine:\"#66cdaa\",mediumblue:\"#0000cd\",mediumorchid:\"#ba55d3\",mediumpurple:\"#9370db\",mediumseagreen:\"#3cb371\",mediumslateblue:\"#7b68ee\",mediumspringgreen:\"#00fa9a\",mediumturquoise:\"#48d1cc\",mediumvioletred:\"#c71585\",midnightblue:\"#191970\",mintcream:\"#f5fffa\",mistyrose:\"#ffe4e1\",\nmoccasin:\"#ffe4b5\",navajowhite:\"#ffdead\",navy:\"#000080\",oldlace:\"#fdf5e6\",olive:\"#808000\",olivedrab:\"#6b8e23\",orange:\"#ffa500\",orangered:\"#ff4500\",orchid:\"#da70d6\",palegoldenrod:\"#eee8aa\",palegreen:\"#98fb98\",paleturquoise:\"#afeeee\",palevioletred:\"#db7093\",papayawhip:\"#ffefd5\",peachpuff:\"#ffdab9\",peru:\"#cd853f\",pink:\"#ffc0cb\",plum:\"#dda0dd\",powderblue:\"#b0e0e6\",purple:\"#800080\",red:\"#ff0000\",rosybrown:\"#bc8f8f\",royalblue:\"#4169e1\",saddlebrown:\"#8b4513\",salmon:\"#fa8072\",sandybrown:\"#f4a460\",seagreen:\"#2e8b57\",\nseashell:\"#fff5ee\",sienna:\"#a0522d\",silver:\"#c0c0c0\",skyblue:\"#87ceeb\",slateblue:\"#6a5acd\",slategray:\"#708090\",slategrey:\"#708090\",snow:\"#fffafa\",springgreen:\"#00ff7f\",steelblue:\"#4682b4\",tan:\"#d2b48c\",teal:\"#008080\",thistle:\"#d8bfd8\",tomato:\"#ff6347\",turquoise:\"#40e0d0\",violet:\"#ee82ee\",wheat:\"#f5deb3\",white:\"#ffffff\",whitesmoke:\"#f5f5f5\",yellow:\"#ffff00\",yellowgreen:\"#9acd32\"};var mc=\"backgroundColor borderTopColor borderRightColor borderBottomColor borderLeftColor color outlineColor\".split(\" \"),nc=/#([0-9a-fA-F])([0-9a-fA-F])([0-9a-fA-F])/,oc=/^#(?:[0-9a-f]{3}){1,2}$/i,pc=/^(?:rgba)?\\((\\d{1,3}),\\s?(\\d{1,3}),\\s?(\\d{1,3}),\\s?(0|1|0\\.\\d*)\\)$/i,qc=/^(?:rgb)?\\((0|[1-9]\\d{0,2}),\\s?(0|[1-9]\\d{0,2}),\\s?(0|[1-9]\\d{0,2})\\)$/i;function rc(a,b){this.code=a;this.a=V[a]||sc;this.message=b||\"\";a=this.a.replace(/((?:^|\\s+)[a-z])/g,function(c){return c.toUpperCase().replace(/^[\\s\\xa0]+/g,\"\")});b=a.length-5;if(0>b||a.indexOf(\"Error\",b)!=b)a+=\"Error\";this.name=a;a=Error(this.message);a.name=this.name;this.stack=a.stack||\"\"}l(rc,Error);var sc=\"unknown error\",V={15:\"element not selectable\",11:\"element not visible\"};V[31]=sc;V[30]=sc;V[24]=\"invalid cookie domain\";V[29]=\"invalid element coordinates\";V[12]=\"invalid element state\";\nV[32]=\"invalid selector\";V[51]=\"invalid selector\";V[52]=\"invalid selector\";V[17]=\"javascript error\";V[405]=\"unsupported operation\";V[34]=\"move target out of bounds\";V[27]=\"no such alert\";V[7]=\"no such element\";V[8]=\"no such frame\";V[23]=\"no such window\";V[28]=\"script timeout\";V[33]=\"session not created\";V[10]=\"stale element reference\";V[21]=\"timeout\";V[25]=\"unable to set cookie\";V[26]=\"unexpected alert open\";V[13]=sc;V[9]=\"unknown command\";var tc=va(),uc=ya()||u(\"iPod\"),vc=u(\"iPad\"),wc=u(\"Android\")&&!(wa()||va()||u(\"Opera\")||u(\"Silk\")),xc=wa(),yc=u(\"Safari\")&&!(wa()||u(\"Coast\")||u(\"Opera\")||u(\"Edge\")||u(\"Edg/\")||u(\"OPR\")||va()||u(\"Silk\")||u(\"Android\"))&&!(ya()||u(\"iPad\")||u(\"iPod\"));function zc(a){return(a=a.exec(t))?a[1]:\"\"}(function(){if(tc)return zc(/Firefox\\/([0-9.]+)/);if(v||Ca||Ba)return Ga;if(xc)return ya()||u(\"iPad\")||u(\"iPod\")?zc(/CriOS\\/([0-9.]+)/):zc(/Chrome\\/([0-9.]+)/);if(yc&&!(ya()||u(\"iPad\")||u(\"iPod\")))return zc(/Version\\/([0-9.]+)/);if(uc||vc){var a=/Version\\/(\\S+).*Mobile\\/(\\S+)/.exec(t);if(a)return a[1]+\".\"+a[2]}else if(wc)return(a=zc(/Android\\s+([0-9.]+)/))?a:zc(/Version\\/([0-9.]+)/);return\"\"})();var Ac=v&&!(9\u003C=Number(La));function W(a,b){b&&\"string\"!==typeof b&&(b=b.toString());return!!a&&1==a.nodeType&&(!b||a.tagName.toUpperCase()==b)};var Bc=function(){var a={K:\"http://www.w3.org/2000/svg\"};return function(b){return a[b]||null}}();\nfunction Cc(a,b){var c=A(a);if(!c.documentElement)return null;(v||wc)&&kc(c?c.parentWindow||c.defaultView:window);try{var d=c.createNSResolver?c.createNSResolver(c.documentElement):Bc;if(v&&!Ka(7))return c.evaluate.call(c,b,a,d,9,null);if(!v||9\u003C=Number(La)){for(var e={},f=c.getElementsByTagName(\"*\"),g=0;g\u003Cf.length;++g){var h=f[g],m=h.namespaceURI;if(m&&!e[m]){var w=h.lookupPrefix(m);if(!w){var r=m.match(\".*/(\\\\w+)/?$\");w=r?r[1]:\"xhtml\"}e[m]=w}}var D={},L;for(L in e)D[e[L]]=L;d=function(M){return D[M]||\nnull}}try{return c.evaluate(b,a,d,9,null)}catch(M){if(\"TypeError\"===M.name)return d=c.createNSResolver?c.createNSResolver(c.documentElement):Bc,c.evaluate(b,a,d,9,null);throw M;}}catch(M){if(!Da||\"NS_ERROR_ILLEGAL_VALUE\"!=M.name)throw new rc(32,\"Unable to locate an element with the xpath expression \"+b+\" because of the following error:\\n\"+M);}}\nfunction Dc(a,b){var c=function(){var d=Cc(b,a);return d?d.singleNodeValue||null:b.selectSingleNode?(d=A(b),d.setProperty&&d.setProperty(\"SelectionLanguage\",\"XPath\"),b.selectSingleNode(a)):null}();if(null!==c&&(!c||1!=c.nodeType))throw new rc(32,'The result of the xpath expression \"'+a+'\" is: '+c+\". It should be an element.\");return c};function Ec(a,b,c,d){this.c=a;this.a=b;this.b=c;this.f=d}Ec.prototype.ceil=function(){this.c=Math.ceil(this.c);this.a=Math.ceil(this.a);this.b=Math.ceil(this.b);this.f=Math.ceil(this.f);return this};Ec.prototype.floor=function(){this.c=Math.floor(this.c);this.a=Math.floor(this.a);this.b=Math.floor(this.b);this.f=Math.floor(this.f);return this};Ec.prototype.round=function(){this.c=Math.round(this.c);this.a=Math.round(this.a);this.b=Math.round(this.b);this.f=Math.round(this.f);return this};function X(a,b,c,d){this.a=a;this.b=b;this.width=c;this.height=d}X.prototype.ceil=function(){this.a=Math.ceil(this.a);this.b=Math.ceil(this.b);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};X.prototype.floor=function(){this.a=Math.floor(this.a);this.b=Math.floor(this.b);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};\nX.prototype.round=function(){this.a=Math.round(this.a);this.b=Math.round(this.b);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};var Fc=\"function\"===typeof ShadowRoot;function Gc(a){for(a=a.parentNode;a&&1!=a.nodeType&&9!=a.nodeType&&11!=a.nodeType;)a=a.parentNode;return W(a)?a:null}\nfunction Y(a,b){b=xa(b);if(\"float\"==b||\"cssFloat\"==b||\"styleFloat\"==b)b=Ac?\"styleFloat\":\"cssFloat\";a:{var c=b;var d=A(a);if(d.defaultView&&d.defaultView.getComputedStyle&&(d=d.defaultView.getComputedStyle(a,null))){c=d[c]||d.getPropertyValue(c)||\"\";break a}c=\"\"}a=c||Hc(a,b);if(null===a)a=null;else if(0\u003C=ja(mc,b)){b:{var e=a.match(pc);if(e&&(b=Number(e[1]),c=Number(e[2]),d=Number(e[3]),e=Number(e[4]),0\u003C=b&&255>=b&&0\u003C=c&&255>=c&&0\u003C=d&&255>=d&&0\u003C=e&&1>=e)){b=[b,c,d,e];break b}b=null}if(!b)b:{if(d=a.match(qc))if(b=\nNumber(d[1]),c=Number(d[2]),d=Number(d[3]),0\u003C=b&&255>=b&&0\u003C=c&&255>=c&&0\u003C=d&&255>=d){b=[b,c,d,1];break b}b=null}if(!b)b:{b=a.toLowerCase();c=lc[b.toLowerCase()];if(!c&&(c=\"#\"==b.charAt(0)?b:\"#\"+b,4==c.length&&(c=c.replace(nc,\"#$1$1$2$2$3$3\")),!oc.test(c))){b=null;break b}b=[parseInt(c.substr(1,2),16),parseInt(c.substr(3,2),16),parseInt(c.substr(5,2),16),1]}a=b?\"rgba(\"+b.join(\", \")+\")\":a}return a}\nfunction Hc(a,b){var c=a.currentStyle||a.style,d=c[b];void 0===d&&\"function\"==ca(c.getPropertyValue)&&(d=c.getPropertyValue(b));return\"inherit\"!=d?void 0!==d?d:null:(a=Gc(a))?Hc(a,b):null}\nfunction Ic(a,b,c){function d(g){var h=Jc(g);return 0\u003Ch.height&&0\u003Ch.width?!0:W(g,\"PATH\")&&(0\u003Ch.height||0\u003Ch.width)?(g=Y(g,\"stroke-width\"),!!g&&0\u003CparseInt(g,10)):\"hidden\"!=Y(g,\"overflow\")&&ma(g.childNodes,function(m){return 3==m.nodeType||W(m)&&d(m)})}function e(g){return Kc(g)==Z&&na(g.childNodes,function(h){return!W(h)||e(h)||!d(h)})}if(!W(a))throw Error(\"Argument to isShown must be of type Element\");if(W(a,\"BODY\"))return!0;if(W(a,\"OPTION\")||W(a,\"OPTGROUP\"))return a=$a(a,function(g){return W(g,\"SELECT\")}),\n!!a&&Ic(a,!0,c);var f=Lc(a);if(f)return!!f.image&&0\u003Cf.rect.width&&0\u003Cf.rect.height&&Ic(f.image,b,c);if(W(a,\"INPUT\")&&\"hidden\"==a.type.toLowerCase()||W(a,\"NOSCRIPT\"))return!1;f=Y(a,\"visibility\");return\"collapse\"!=f&&\"hidden\"!=f&&c(a)&&(b||0!=Mc(a))&&d(a)?!e(a):!1}var Z=\"hidden\";\nfunction Kc(a){function b(p){function q(fb){if(fb==g)return!0;var Wb=Y(fb,\"display\");return 0==Wb.lastIndexOf(\"inline\",0)||\"contents\"==Wb||\"absolute\"==Xb&&\"static\"==Y(fb,\"position\")?!1:!0}var Xb=Y(p,\"position\");if(\"fixed\"==Xb)return w=!0,p==g?null:g;for(p=Gc(p);p&&!q(p);)p=Gc(p);return p}function c(p){var q=p;if(\"visible\"==m)if(p==g&&h)q=h;else if(p==h)return{x:\"visible\",y:\"visible\"};q={x:Y(q,\"overflow-x\"),y:Y(q,\"overflow-y\")};p==g&&(q.x=\"visible\"==q.x?\"auto\":q.x,q.y=\"visible\"==q.y?\"auto\":q.y);return q}\nfunction d(p){if(p==g){var q=(new ab(f)).a;p=q.scrollingElement?q.scrollingElement:Ea||\"CSS1Compat\"!=q.compatMode?q.body||q.documentElement:q.documentElement;q=q.parentWindow||q.defaultView;p=v&&Ka(\"10\")&&q.pageYOffset!=p.scrollTop?new Ua(p.scrollLeft,p.scrollTop):new Ua(q.pageXOffset||p.scrollLeft,q.pageYOffset||p.scrollTop)}else p=new Ua(p.scrollLeft,p.scrollTop);return p}var e=Nc(a),f=A(a),g=f.documentElement,h=f.body,m=Y(g,\"overflow\"),w;for(a=b(a);a;a=b(a)){var r=c(a);if(\"visible\"!=r.x||\"visible\"!=\nr.y){var D=Jc(a);if(0==D.width||0==D.height)return Z;var L=e.a\u003CD.a,M=e.b\u003CD.b;if(L&&\"hidden\"==r.x||M&&\"hidden\"==r.y)return Z;if(L&&\"visible\"!=r.x||M&&\"visible\"!=r.y){L=d(a);M=e.b\u003CD.b-L.y;if(e.a\u003CD.a-L.x&&\"visible\"!=r.x||M&&\"visible\"!=r.x)return Z;e=Kc(a);return e==Z?Z:\"scroll\"}L=e.f>=D.a+D.width;D=e.c>=D.b+D.height;if(L&&\"hidden\"==r.x||D&&\"hidden\"==r.y)return Z;if(L&&\"visible\"!=r.x||D&&\"visible\"!=r.y){if(w&&(r=d(a),e.f>=g.scrollWidth-r.x||e.a>=g.scrollHeight-r.y))return Z;e=Kc(a);return e==Z?Z:\"scroll\"}}}return\"none\"}\nfunction Jc(a){var b=Lc(a);if(b)return b.rect;if(W(a,\"HTML\"))return a=A(a),a=((a?a.parentWindow||a.defaultView:window)||window).document,a=\"CSS1Compat\"==a.compatMode?a.documentElement:a.body,a=new Va(a.clientWidth,a.clientHeight),new X(0,0,a.width,a.height);try{var c=a.getBoundingClientRect()}catch(d){return new X(0,0,0,0)}b=new X(c.left,c.top,c.right-c.left,c.bottom-c.top);v&&a.ownerDocument.body&&(a=A(a),b.a-=a.documentElement.clientLeft+a.body.clientLeft,b.b-=a.documentElement.clientTop+a.body.clientTop);\nreturn b}function Lc(a){var b=W(a,\"MAP\");if(!b&&!W(a,\"AREA\"))return null;var c=b?a:W(a.parentNode,\"MAP\")?a.parentNode:null,d=null,e=null;c&&c.name&&(d=Dc('/descendant::*[@usemap = \"#'+c.name+'\"]',A(c)))&&(e=Jc(d),b||\"default\"==a.shape.toLowerCase()||(a=Oc(a),b=Math.min(Math.max(a.a,0),e.width),c=Math.min(Math.max(a.b,0),e.height),e=new X(b+e.a,c+e.b,Math.min(a.width,e.width-b),Math.min(a.height,e.height-c))));return{image:d,rect:e||new X(0,0,0,0)}}\nfunction Oc(a){var b=a.shape.toLowerCase();a=a.coords.split(\",\");if(\"rect\"==b&&4==a.length){b=a[0];var c=a[1];return new X(b,c,a[2]-b,a[3]-c)}if(\"circle\"==b&&3==a.length)return b=a[2],new X(a[0]-b,a[1]-b,2*b,2*b);if(\"poly\"==b&&2\u003Ca.length){b=a[0];c=a[1];for(var d=b,e=c,f=2;f+1\u003Ca.length;f+=2)b=Math.min(b,a[f]),d=Math.max(d,a[f]),c=Math.min(c,a[f+1]),e=Math.max(e,a[f+1]);return new X(b,c,d-b,e-c)}return new X(0,0,0,0)}function Nc(a){a=Jc(a);return new Ec(a.b,a.a+a.width,a.b+a.height,a.a)}\nfunction Mc(a){if(Ac){if(\"relative\"==Y(a,\"position\"))return 1;a=Y(a,\"filter\");return(a=a.match(/^alpha\\(opacity=(\\d*)\\)/)||a.match(/^progid:DXImageTransform.Microsoft.Alpha\\(Opacity=(\\d*)\\)/))?Number(a[1])/100:1}return Pc(a)}function Pc(a){var b=1,c=Y(a,\"opacity\");c&&(b=Number(c));(a=Gc(a))&&(b*=Pc(a));return b};ba(\"_\",function(a,b){function c(d){if(W(d)&&\"none\"==Y(d,\"display\"))return!1;var e;if((e=d.parentNode)&&e.shadowRoot&&void 0!==d.assignedSlot)e=d.assignedSlot?d.assignedSlot.parentNode:null;else if(d.getDestinationInsertionPoints){var f=d.getDestinationInsertionPoints();0\u003Cf.length&&(e=f[f.length-1])}if(Fc&&e instanceof ShadowRoot){if(e.host.shadowRoot!==e)return!1;e=e.host}return!e||9!=e.nodeType&&11!=e.nodeType?e&&W(e,\"DETAILS\")&&!e.open&&!W(d,\"SUMMARY\")?!1:!!e&&c(e):!0}return Ic(a,!!b,c)});; return this._.apply(null,arguments);}).apply({navigator:typeof window!='undefined'?window.navigator:null,document:typeof window!='undefined'?window.document:null}, arguments);}\n).apply(null, arguments);"
}
[**********.071][INFO]: Waiting for pending navigations...
[**********.071][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=212) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.072][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=212) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.072][INFO]: Done waiting for pending navigations. Status: ok
[**********.075][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=213) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.090][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=213) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.090][INFO]: Waiting for pending navigations...
[**********.090][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=214) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.091][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=214) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.091][INFO]: Done waiting for pending navigations. Status: ok
[**********.091][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ExecuteScript true
[**********.093][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND IsElementEnabled {
   "id": "ad91d644-1af6-4960-a246-81b5872f40b8"
}
[**********.093][INFO]: Waiting for pending navigations...
[**********.093][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=215) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.094][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=215) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.094][INFO]: Done waiting for pending navigations. Status: ok
[**********.094][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=216) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.contentType",
   "returnByValue": true
}
[**********.096][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=216) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "text/html"
   }
}
[**********.098][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=217) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.106][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=217) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": true
      }
   }
}
[**********.107][INFO]: Waiting for pending navigations...
[**********.107][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=218) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.108][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=218) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.108][INFO]: Done waiting for pending navigations. Status: ok
[**********.108][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE IsElementEnabled true
[**********.110][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND ExecuteScript {
   "args": [ {
      "element-6066-11e4-a52e-4f735466cecf": "ad91d644-1af6-4960-a246-81b5872f40b8"
   } ],
   "script": "arguments[0].click();"
}
[**********.110][INFO]: Waiting for pending navigations...
[**********.110][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=219) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.111][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=219) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.111][INFO]: Done waiting for pending navigations. Status: ok
[**********.111][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=220) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": true,
   "expression": "(function() { // Copyright 2012 The Chromium Authors\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\n/**\n * Enum for WebDriver status codes....",
   "returnByValue": true
}
[**********.130][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=220) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "object",
      "value": {
         "status": 0,
         "value": null
      }
   }
}
[**********.130][INFO]: Waiting for pending navigations...
[**********.130][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=221) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "expression": "1"
}
[**********.132][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=221) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "description": "1",
      "type": "number",
      "value": 1
   }
}
[**********.132][INFO]: Done waiting for pending navigations. Status: ok
[**********.132][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE ExecuteScript null
[**********.868][DEBUG]: DevTools WebSocket Event: Log.entryAdded (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "entry": {
      "level": "error",
      "networkRequestId": "15528.316",
      "source": "network",
      "text": "Failed to load resource: the server responded with a status of 403 ()",
      "timestamp": 1.749127468866508e+12,
      "url": "https://api.x.com/graphql/8BE68tscBnbMD7DyGOZxRg/Viewer?variables=%7B%22withCommunitiesMemberships%22%3Atrue%7D&features=%7B%22subscriptions_upsells_api_enabled%22%3Atrue%2C%22profile_label_improve..."
   }
}
[1749127469.328][DEBUG]: DevTools WebSocket Event: Page.frameScheduledNavigation (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "delay": 0,
   "frameId": "7C06568396BD027AF2A2803C59BE8E07",
   "reason": "scriptInitiated",
   "url": "https://x.com/home"
}
[1749127469.328][DEBUG]: DevTools WebSocket Event: Page.frameRequestedNavigation (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "disposition": "currentTab",
   "frameId": "7C06568396BD027AF2A2803C59BE8E07",
   "reason": "scriptInitiated",
   "url": "https://x.com/home"
}
[1749127469.328][DEBUG]: DevTools WebSocket Event: Page.frameStartedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07"
}
[1749127469.341][DEBUG]: DevTools WebSocket Event: Page.frameClearedScheduledNavigation (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07"
}
[1749127469.684][DEBUG]: DevTools WebSocket Event: Runtime.executionContextsCleared (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
}
[1749127469.684][DEBUG]: DevTools WebSocket Event: Page.frameNavigated (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frame": {
      "adFrameStatus": {
         "adFrameType": "none"
      },
      "crossOriginIsolatedContextType": "NotIsolated",
      "domainAndRegistry": "x.com",
      "gatedAPIFeatures": [  ],
      "id": "7C06568396BD027AF2A2803C59BE8E07",
      "loaderId": "767D8A3CA76C2DDDB356E5E6707AD6A0",
      "mimeType": "text/html",
      "secureContextType": "Secure",
      "securityOrigin": "https://x.com",
      "url": "https://x.com/home"
   },
   "type": "Navigation"
}
[1749127469.684][DEBUG]: DevTools WebSocket Event: Runtime.executionContextCreated (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "context": {
      "auxData": {
         "frameId": "7C06568396BD027AF2A2803C59BE8E07",
         "isDefault": true,
         "type": "default"
      },
      "id": 2,
      "name": "",
      "origin": "https://x.com",
      "uniqueId": "-4297374335407415482.-4244537142782305942"
   }
}
[1749127470.326][DEBUG]: DevTools WebSocket Event: Page.navigatedWithinDocument (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07",
   "url": "https://x.com/home"
}
[1749127470.332][DEBUG]: DevTools WebSocket Event: Page.domContentEventFired (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "timestamp": 44635.697523
}
[1749127470.421][DEBUG]: DevTools WebSocket Event: Page.loadEventFired (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "timestamp": 44635.783757
}
[1749127470.421][DEBUG]: DevTools WebSocket Command: Runtime.evaluate (id=222) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "awaitPromise": false,
   "expression": "document.readyState",
   "returnByValue": true
}
[1749127470.421][DEBUG]: DevTools WebSocket Event: Page.frameStoppedLoading (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "frameId": "7C06568396BD027AF2A2803C59BE8E07"
}
[1749127470.432][DEBUG]: DevTools WebSocket Response: Runtime.evaluate (id=222) (session_id=0C7E126E74C468CEE17CF3D378841D60) 7C06568396BD027AF2A2803C59BE8E07 {
   "result": {
      "type": "string",
      "value": "complete"
   }
}
[1749127478.138][INFO]: [98e435b56d0e8e201713f0d012b77a2b] COMMAND Quit {
}
[1749127478.138][DEBUG]: DevTools WebSocket Command: Browser.close (id=223) (session_id=) browser {
}
[1749127478.277][INFO]: [98e435b56d0e8e201713f0d012b77a2b] RESPONSE Quit
[1749127478.277][DEBUG]: Log type 'driver' lost 2 entries on destruction
[1749127478.277][DEBUG]: Log type 'browser' lost 1 entries on destruction
