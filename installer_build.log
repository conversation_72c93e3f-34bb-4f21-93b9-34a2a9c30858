2025-06-05 15:51:57,656 - INFO - ============================================================
2025-06-05 15:51:57,657 - INFO - Starting Sorcerio Professional Installer Build
2025-06-05 15:51:57,657 - INFO - ============================================================
2025-06-05 15:51:57,657 - INFO - 
--- Cleaning previous builds ---
2025-06-05 15:51:57,658 - INFO - Cleaning previous build artifacts...
2025-06-05 15:51:57,658 - INFO - Removed dist directory
2025-06-05 15:51:57,660 - INFO - Removed build directory
2025-06-05 15:51:57,661 - ERROR - Build failed at step: Cleaning previous builds
2025-06-05 15:52:21,969 - INFO - ============================================================
2025-06-05 15:52:21,969 - INFO - Starting Sorcerio Professional Installer Build
2025-06-05 15:52:21,970 - INFO - ============================================================
2025-06-05 15:52:21,970 - INFO - 
--- Cleaning previous builds ---
2025-06-05 15:52:21,970 - INFO - Cleaning previous build artifacts...
2025-06-05 15:52:21,971 - INFO - Cleanup completed successfully
2025-06-05 15:52:21,971 - INFO - 
--- Verifying dependencies ---
2025-06-05 15:52:21,972 - INFO - Verifying dependencies...
2025-06-05 15:52:21,973 - INFO - All dependencies verified successfully
2025-06-05 15:52:21,973 - INFO - 
--- Installing requirements ---
2025-06-05 15:52:21,973 - INFO - Installing Python requirements...
2025-06-05 15:52:26,155 - INFO - Requirements installed successfully
2025-06-05 15:52:26,155 - INFO - 
--- Building executable ---
2025-06-05 15:52:26,156 - INFO - Building standalone executable with PyInstaller...
2025-06-05 15:52:26,156 - INFO - Running: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe -m PyInstaller --clean --noconfirm C:\Users\<USER>\PycharmProjects\SorcerioModules\sorcerio_professional.spec
2025-06-05 15:52:27,713 - ERROR - PyInstaller failed with return code 1
2025-06-05 15:52:27,714 - ERROR - STDOUT: 
2025-06-05 15:52:27,715 - ERROR - STDERR: 1252 INFO: PyInstaller: 5.13.2
1252 INFO: Python: 3.10.0
1273 INFO: Platform: Windows-10-10.0.17763-SP0
1277 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", line 198, in <module>
    run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", line 180, in run
    run_build(pyi_config, spec_file, **vars(args))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", line 61, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py", line 1019, in main
    build(specfile, distpath, workpath, clean_build)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py", line 944, in build
    exec(code, spec_namespace)
  File "C:\Users\<USER>\PycharmProjects\SorcerioModules\sorcerio_professional.spec", line 139, in <module>
    a = Analysis(
TypeError: Analysis.__init__() got an unexpected keyword argument 'optimize'

2025-06-05 15:52:27,716 - ERROR - Build failed at step: Building executable
2025-06-05 15:52:58,114 - INFO - ============================================================
2025-06-05 15:52:58,115 - INFO - Starting Sorcerio Professional Installer Build
2025-06-05 15:52:58,115 - INFO - ============================================================
2025-06-05 15:52:58,115 - INFO - 
--- Cleaning previous builds ---
2025-06-05 15:52:58,116 - INFO - Cleaning previous build artifacts...
2025-06-05 15:52:58,116 - INFO - Removed dist directory
2025-06-05 15:52:58,118 - INFO - Removed build directory
2025-06-05 15:52:58,119 - INFO - Cleanup completed successfully
2025-06-05 15:52:58,119 - INFO - 
--- Verifying dependencies ---
2025-06-05 15:52:58,119 - INFO - Verifying dependencies...
2025-06-05 15:52:58,120 - INFO - All dependencies verified successfully
2025-06-05 15:52:58,121 - INFO - 
--- Installing requirements ---
2025-06-05 15:52:58,121 - INFO - Installing Python requirements...
2025-06-05 15:53:02,111 - INFO - Requirements installed successfully
2025-06-05 15:53:02,112 - INFO - 
--- Building executable ---
2025-06-05 15:53:02,112 - INFO - Building standalone executable with PyInstaller...
2025-06-05 15:53:02,112 - INFO - Running: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe -m PyInstaller --clean --noconfirm C:\Users\<USER>\PycharmProjects\SorcerioModules\sorcerio_professional.spec
2025-06-05 15:53:44,674 - ERROR - PyInstaller failed with return code 1
2025-06-05 15:53:44,674 - ERROR - STDOUT: Adding imports: ['yt_dlp.compat._legacy', 'yt_dlp.compat._deprecated', 'yt_dlp.utils._legacy', 'yt_dlp.utils._deprecated', 'Cryptodome', 'websockets', 'websockets.__main__', 'websockets.asyncio', 'websockets.asyncio.async_timeout', 'websockets.asyncio.client', 'websockets.asyncio.compatibility', 'websockets.asyncio.connection', 'websockets.asyncio.messages', 'websockets.asyncio.router', 'websockets.asyncio.server', 'websockets.auth', 'websockets.cli', 'websockets.client', 'websockets.connection', 'websockets.datastructures', 'websockets.exceptions', 'websockets.extensions', 'websockets.extensions.base', 'websockets.extensions.permessage_deflate', 'websockets.frames', 'websockets.headers', 'websockets.http', 'websockets.http11', 'websockets.imports', 'websockets.legacy', 'websockets.legacy.auth', 'websockets.legacy.client', 'websockets.legacy.exceptions', 'websockets.legacy.framing', 'websockets.legacy.handshake', 'websockets.legacy.http', 'websockets.legacy.protocol', 'websockets.legacy.server', 'websockets.protocol', 'websockets.server', 'websockets.speedups', 'websockets.streams', 'websockets.sync', 'websockets.sync.client', 'websockets.sync.connection', 'websockets.sync.messages', 'websockets.sync.router', 'websockets.sync.server', 'websockets.sync.utils', 'websockets.typing', 'websockets.uri', 'websockets.utils', 'websockets.version', 'requests', 'requests.__version__', 'requests._internal_utils', 'requests.adapters', 'requests.api', 'requests.auth', 'requests.certs', 'requests.compat', 'requests.cookies', 'requests.exceptions', 'requests.help', 'requests.hooks', 'requests.models', 'requests.packages', 'requests.sessions', 'requests.status_codes', 'requests.structures', 'requests.utils', 'urllib3', 'urllib3._base_connection', 'urllib3._collections', 'urllib3._request_methods', 'urllib3._version', 'urllib3.connection', 'urllib3.connectionpool', 'urllib3.contrib', 'urllib3.contrib.pyopenssl', 'urllib3.contrib.socks', 'urllib3.exceptions', 'urllib3.fields', 'urllib3.filepost', 'urllib3.http2', 'urllib3.poolmanager', 'urllib3.response', 'urllib3.util', 'urllib3.util.connection', 'urllib3.util.proxy', 'urllib3.util.request', 'urllib3.util.response', 'urllib3.util.retry', 'urllib3.util.ssl_', 'urllib3.util.ssl_match_hostname', 'urllib3.util.ssltransport', 'urllib3.util.timeout', 'urllib3.util.url', 'urllib3.util.util', 'urllib3.util.wait', 'mutagen', 'brotli', 'certifi', 'secretstorage', 'curl_cffi']

2025-06-05 15:53:44,675 - ERROR - STDERR: 1188 INFO: PyInstaller: 5.13.2
1188 INFO: Python: 3.10.0
1204 INFO: Platform: Windows-10-10.0.17763-SP0
1207 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
1211 INFO: Extending PYTHONPATH with paths
['C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules',
 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules']
3133 INFO: Appending 'datas' from .spec
3160 INFO: checking Analysis
3160 INFO: Building Analysis because Analysis-00.toc is non existent
3160 INFO: Initializing module dependency graph...
3165 INFO: Caching module graph hooks...
3222 INFO: Analyzing base_library.zip ...
6628 INFO: Loading module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'...
7263 INFO: Loading module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'...
9883 INFO: Loading module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'...
12187 INFO: Caching module dependency graph...
12536 INFO: running Analysis Analysis-00.toc
12578 INFO: Adding Microsoft.Windows.Common-Controls to dependent assemblies of final executable
  required by C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe
12697 INFO: Analyzing C:\Users\<USER>\PycharmProjects\SorcerioModules\main.py
12772 INFO: Loading module hook 'hook-PyQt5.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'...
13525 INFO: Loading module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'...
13672 INFO: Loading module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
15611 INFO: Loading module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
15887 INFO: Loading module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
16519 INFO: Loading module hook 'hook-yt_dlp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\yt_dlp\\__pyinstaller'...
842 WARNING: Failed to collect submodules for 'urllib3.contrib.emscripten' because importing 'urllib3.contrib.emscripten' raised: ModuleNotFoundError: No module named 'js'
19925 WARNING: collect_data_files - skipping data collection for module 'curl_cffi' as it is not a package.
21006 INFO: Loading module hook 'hook-sqlite3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'...
22134 INFO: Loading module hook 'hook-websockets.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
23786 INFO: Loading module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'...
25537 INFO: Loading module hook 'hook-Cryptodome.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
26724 INFO: Loading module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'...
27596 INFO: Processing pre-safe import module hook distutils from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module\\hook-distutils.py'.
27715 INFO: Loading module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site-packages\\PyInstaller\\hooks'...
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 196, in _run_module_as_main
    return _run_code(code, main_globals, None,
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\runpy.py", line 86, in _run_code
    exec(code, run_globals)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", line 198, in <module>
    run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", line 180, in run
    run_build(pyi_config, spec_file, **vars(args))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\__main__.py", line 61, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py", line 1019, in main
    build(specfile, distpath, workpath, clean_build)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py", line 944, in build
    exec(code, spec_namespace)
  File "C:\Users\<USER>\PycharmProjects\SorcerioModules\sorcerio_professional.spec", line 139, in <module>
    a = Analysis(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py", line 429, in __init__
    self.__postinit__()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\datastruct.py", line 184, in __postinit__
    self.assemble()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\building\build_main.py", line 590, in assemble
    priority_scripts.append(self.graph.add_script(script))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\depend\analysis.py", line 268, in add_script
    self._top_script_node = super().add_script(pathname)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1359, in add_script
    self._process_imports(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2811, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\depend\analysis.py", line 432, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2262, in _safe_import_hook
    target_modules = self.import_hook(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1431, in import_hook
    target_package, target_module_partname = self._find_head_package(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1610, in _find_head_package
    target_package = self._safe_import_module(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\depend\analysis.py", line 479, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1992, in _safe_import_module
    self._process_imports(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2811, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\depend\analysis.py", line 432, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2262, in _safe_import_hook
    target_modules = self.import_hook(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1431, in import_hook
    target_package, target_module_partname = self._find_head_package(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1610, in _find_head_package
    target_package = self._safe_import_module(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\depend\analysis.py", line 479, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1992, in _safe_import_module
    self._process_imports(n)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2811, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\depend\analysis.py", line 432, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2262, in _safe_import_hook
    target_modules = self.import_hook(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1431, in import_hook
    target_package, target_module_partname = self._find_head_package(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1610, in _find_head_package
    target_package = self._safe_import_module(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\depend\analysis.py", line 479, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1991, in _safe_import_module
    n = self._scan_code(module, co, co_ast)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2606, in _scan_code
    self._scan_bytecode(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2710, in _scan_bytecode
    for inst in util.iterate_instructions(module_code_object):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\util.py", line 44, in iterate_instructions
    yield from iterate_instructions(constant)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\util.py", line 36, in iterate_instructions
    yield from (i for i in get_instructions(code_object) if i.opname != "EXTENDED_ARG")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\PyInstaller\lib\modulegraph\util.py", line 36, in <genexpr>
    yield from (i for i in get_instructions(code_object) if i.opname != "EXTENDED_ARG")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\dis.py", line 338, in _get_instructions_bytes
    argval, argrepr = _get_const_info(arg, constants)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\dis.py", line 292, in _get_const_info
    argval = const_list[const_index]
IndexError: tuple index out of range

2025-06-05 15:53:44,679 - ERROR - Build failed at step: Building executable
2025-06-05 15:54:43,271 - INFO - ============================================================
2025-06-05 15:54:43,272 - INFO - Starting Sorcerio Professional Installer Build
2025-06-05 15:54:43,272 - INFO - ============================================================
2025-06-05 15:54:43,272 - INFO - 
--- Cleaning previous builds ---
2025-06-05 15:54:43,273 - INFO - Cleaning previous build artifacts...
2025-06-05 15:54:43,273 - INFO - Removed dist directory
2025-06-05 15:54:43,276 - INFO - Removed build directory
2025-06-05 15:54:43,276 - INFO - Cleanup completed successfully
2025-06-05 15:54:43,277 - INFO - 
--- Verifying dependencies ---
2025-06-05 15:54:43,277 - INFO - Verifying dependencies...
2025-06-05 15:54:43,278 - INFO - All dependencies verified successfully
2025-06-05 15:54:43,279 - INFO - 
--- Installing requirements ---
2025-06-05 15:54:43,279 - INFO - Installing Python requirements...
2025-06-05 15:54:47,303 - INFO - Requirements installed successfully
2025-06-05 15:54:47,303 - INFO - 
--- Building executable ---
2025-06-05 15:54:47,303 - INFO - Building standalone executable with PyInstaller...
2025-06-05 15:54:47,304 - INFO - Running: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe -m PyInstaller --clean --noconfirm C:\Users\<USER>\PycharmProjects\SorcerioModules\sorcerio_simple.spec
2025-06-05 16:01:19,060 - ERROR - Executable not found at C:\Users\<USER>\PycharmProjects\SorcerioModules\dist\Sorcerio\Sorcerio.exe
2025-06-05 16:01:19,061 - ERROR - Build failed at step: Building executable
2025-06-05 16:15:30,507 - INFO - ============================================================
2025-06-05 16:15:30,508 - INFO - Starting Sorcerio Professional Installer Build
2025-06-05 16:15:30,509 - INFO - ============================================================
2025-06-05 16:15:30,509 - INFO - 
--- Cleaning previous builds ---
2025-06-05 16:15:30,510 - INFO - Cleaning previous build artifacts...
2025-06-05 16:15:30,588 - INFO - Removed dist directory
2025-06-05 16:15:30,664 - INFO - Removed build directory
2025-06-05 16:15:30,667 - INFO - Cleanup completed successfully
2025-06-05 16:15:30,667 - INFO - 
--- Verifying dependencies ---
2025-06-05 16:15:30,668 - INFO - Verifying dependencies...
2025-06-05 16:15:30,669 - INFO - All dependencies verified successfully
2025-06-05 16:15:30,670 - INFO - 
--- Installing requirements ---
2025-06-05 16:15:30,670 - INFO - Installing Python requirements...
2025-06-05 16:15:35,308 - INFO - Requirements installed successfully
2025-06-05 16:15:35,308 - INFO - 
--- Building executable ---
2025-06-05 16:15:35,309 - INFO - Building standalone executable with PyInstaller...
2025-06-05 16:15:35,309 - INFO - Running: C:\Users\<USER>\PycharmProjects\SorcerioModules\.venv\Scripts\python.exe -m PyInstaller --clean --noconfirm C:\Users\<USER>\PycharmProjects\SorcerioModules\sorcerio_simple.spec
2025-06-05 16:19:56,041 - ERROR - PyInstaller failed with return code 1
2025-06-05 16:19:56,042 - ERROR - STDOUT: 
2025-06-05 16:19:56,043 - ERROR - STDERR: 514 INFO: PyInstaller: 6.14.0, contrib hooks: 2025.4
514 INFO: Python: 3.10.0
528 INFO: Platform: Windows-10-10.0.17763-SP0
528 INFO: Python environment: C:\Users\<USER>\PycharmProjects\SorcerioModules\.venv
534 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
538 INFO: Module search paths (PYTHONPATH):
['C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.zip',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310',
 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv',
 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages',
 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules',
 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules']
2293 INFO: Appending 'datas' from .spec
2318 INFO: checking Analysis
2318 INFO: Building Analysis because Analysis-00.toc is non existent
2319 INFO: Running Analysis Analysis-00.toc
2319 INFO: Target bytecode optimization level: 0
2319 INFO: Initializing module dependency graph...
2321 INFO: Initializing module graph hook caches...
2415 INFO: Analyzing modules for base_library.zip ...
4119 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
4266 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
6757 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
8137 INFO: Caching module dependency graph...
8244 INFO: Looking for Python shared library...
8259 INFO: Using Python shared library: C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python310.dll
8259 INFO: Analyzing C:\Users\<USER>\PycharmProjects\SorcerioModules\main.py
8345 INFO: Processing standard module hook 'hook-PyQt5.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
9732 INFO: Processing standard module hook 'hook-PyQt5.QtCore.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
11547 INFO: Processing standard module hook 'hook-PyQt5.QtWidgets.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
13052 INFO: Processing standard module hook 'hook-PyQt5.QtGui.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
15074 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15540 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
15542 INFO: SetuptoolsInfo: initializing cached setuptools info...
17641 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
17942 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
18377 INFO: Processing standard module hook 'hook-appdirs.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
18419 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
18560 INFO: Processing standard module hook 'hook-cv2.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
19615 INFO: Processing standard module hook 'hook-numpy.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
20371 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
21578 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
22100 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
22205 INFO: Processing standard module hook 'hook-psutil.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
24274 INFO: Processing standard module hook 'hook-selenium.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
27374 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
27491 INFO: Processing standard module hook 'hook-websockets.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29596 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
29778 INFO: Processing standard module hook 'hook-PyQt5.QtWebChannel.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
30138 INFO: Processing standard module hook 'hook-PyQt5.QtWebEngineWidgets.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
30449 INFO: Processing standard module hook 'hook-PyQt5.QtWebEngineCore.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
31112 INFO: Processing standard module hook 'hook-PyQt5.QtNetwork.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
31832 INFO: Processing standard module hook 'hook-PyQt5.QtPrintSupport.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
32110 INFO: Processing standard module hook 'hook-apscheduler.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
33100 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
33481 INFO: Analyzing hidden import 'apscheduler.triggers.cron'
33519 INFO: Processing module hooks (post-graph stage)...
34295 WARNING: Hidden import "sip" not found!
34409 INFO: Processing standard module hook 'hook-PyQt5.QtPositioning.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
34933 INFO: Processing standard module hook 'hook-PyQt5.QtQuick.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
35325 INFO: Processing standard module hook 'hook-PyQt5.QtQml.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
37546 INFO: Processing standard module hook 'hook-PyQt5.QtQuickWidgets.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
37785 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
39284 INFO: Performing binary vs. data reclassification (2262 entries)
52965 INFO: Looking for ctypes DLLs
53009 INFO: Analyzing run-time hooks ...
53017 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
53043 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
53061 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
53081 INFO: Including run-time hook 'pyi_rth_pyqt5.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\rthooks'
53099 INFO: Processing pre-find-module-path hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
53115 INFO: Processing standard module hook 'hook-_pyi_rth_utils.py' from 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyInstaller\\hooks'
53388 INFO: Creating base_library.zip...
53595 INFO: Looking for dynamic libraries
55138 INFO: Extra DLL search directories (AddDllDirectory): ['C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin', 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\numpy.libs', 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\cv2\\../../x64/vc14/bin']
55138 INFO: Extra DLL search directories (PATH): ['C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\Lib\\site-packages\\cv2\\../../x64/vc14/bin', 'C:\\Users\\<USER>\\PycharmProjects\\SorcerioModules\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin']
61987 INFO: Warnings written to C:\Users\<USER>\PycharmProjects\SorcerioModules\build\sorcerio_simple\warn-sorcerio_simple.txt
62233 INFO: Graph cross-reference written to C:\Users\<USER>\PycharmProjects\SorcerioModules\build\sorcerio_simple\xref-sorcerio_simple.html
62580 INFO: checking PYZ
62580 INFO: Building PYZ because PYZ-00.toc is non existent
62580 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\PycharmProjects\SorcerioModules\build\sorcerio_simple\PYZ-00.pyz
64480 INFO: Building PYZ (ZlibArchive) C:\Users\<USER>\PycharmProjects\SorcerioModules\build\sorcerio_simple\PYZ-00.pyz completed successfully.
64695 INFO: checking PKG
64695 INFO: Building PKG because PKG-00.toc is non existent
64695 INFO: Building PKG (CArchive) Sorcerio.pkg
260155 INFO: Building PKG (CArchive) Sorcerio.pkg completed successfully.
260330 INFO: Bootloader C:\Users\<USER>\PycharmProjects\SorcerioModules\.venv\lib\site-packages\PyInstaller\bootloader\Windows-64bit-intel\runw.exe
260331 INFO: checking EXE
260331 INFO: Building EXE because EXE-00.toc is non existent
260331 INFO: Building EXE from EXE-00.toc
Fatal error: PyInstaller does not include a pre-compiled bootloader for your
platform. For more details and instructions how to build the bootloader see
<https://pyinstaller.readthedocs.io/en/stable/bootloader-building.html>

2025-06-05 16:19:56,055 - ERROR - Build failed at step: Building executable
