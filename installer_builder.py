#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SorcerioModules Professional Windows Installer Builder
Creates a complete standalone Windows installer with all dependencies bundled.
"""

import os
import sys
import shutil
import subprocess
import logging
import zipfile
import requests
from pathlib import Path
import tempfile

# Configuration
COMPANY_NAME = "Ozmorph"
APP_NAME = "Sorcerio"
APP_VERSION = "1.0.0"
INSTALLER_OUTPUT_DIR = Path("C:/Users/<USER>/Desktop/sorcsetup")
PROJECT_ROOT = Path(__file__).resolve().parent

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SorcerioInstallerBuilder:
    def __init__(self):
        self.temp_dir = Path(tempfile.mkdtemp(prefix="sorcerio_build_"))
        self.build_dir = self.temp_dir / "build"
        self.dist_dir = self.temp_dir / "dist"
        self.installer_dir = self.temp_dir / "installer"
        
        # Create directories
        for dir_path in [self.build_dir, self.dist_dir, self.installer_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
            
        logger.info(f"Build environment created at: {self.temp_dir}")

    def download_python_embeddable(self):
        """Download Python embeddable distribution"""
        logger.info("Downloading Python embeddable distribution...")
        
        python_url = "https://www.python.org/ftp/python/3.10.11/python-3.10.11-embed-amd64.zip"
        python_zip = self.temp_dir / "python-embed.zip"
        python_dir = self.build_dir / "python"
        
        # Download Python
        response = requests.get(python_url, stream=True)
        response.raise_for_status()
        
        with open(python_zip, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # Extract Python
        with zipfile.ZipFile(python_zip, 'r') as zip_ref:
            zip_ref.extractall(python_dir)
        
        # Configure Python path
        pth_file = python_dir / "python310._pth"
        with open(pth_file, 'w') as f:
            f.write("python310.zip\n")
            f.write(".\n")
            f.write("Lib\n")
            f.write("Lib/site-packages\n")
            f.write("import site\n")
        
        logger.info("Python embeddable distribution downloaded and configured")
        return python_dir

    def install_dependencies(self, python_dir):
        """Install all Python dependencies"""
        logger.info("Installing Python dependencies...")
        
        # Download get-pip.py
        pip_url = "https://bootstrap.pypa.io/get-pip.py"
        get_pip = self.temp_dir / "get-pip.py"
        
        response = requests.get(pip_url)
        response.raise_for_status()
        
        with open(get_pip, 'w') as f:
            f.write(response.text)
        
        # Install pip
        python_exe = python_dir / "python.exe"
        subprocess.run([str(python_exe), str(get_pip)], check=True)
        
        # Install requirements
        requirements_file = PROJECT_ROOT / "requirements.txt"
        pip_exe = python_dir / "Scripts" / "pip.exe"
        
        subprocess.run([
            str(pip_exe), "install", "-r", str(requirements_file),
            "--no-warn-script-location"
        ], check=True)
        
        logger.info("Dependencies installed successfully")

    def copy_application_files(self):
        """Copy all application files to build directory"""
        logger.info("Copying application files...")
        
        app_dir = self.build_dir / "app"
        app_dir.mkdir(exist_ok=True)
        
        # Core Python files
        core_files = [
            "main.py", "ui.py", "download.py", "upload.py", 
            "stats.py", "utils.py", "tailscale_manager.py"
        ]
        
        for file_name in core_files:
            src = PROJECT_ROOT / file_name
            if src.exists():
                shutil.copy2(src, app_dir / file_name)
        
        # Copy directories
        directories_to_copy = [
            "configuration", "videos", "portable_chromium", 
            "live_feed_thumbnails"
        ]
        
        for dir_name in directories_to_copy:
            src_dir = PROJECT_ROOT / dir_name
            if src_dir.exists():
                dst_dir = app_dir / dir_name
                if dst_dir.exists():
                    shutil.rmtree(dst_dir)
                shutil.copytree(src_dir, dst_dir)
        
        # Copy icon files
        icon_files = ["instagram.ico", "x.ico"]
        for icon_file in icon_files:
            src = PROJECT_ROOT / icon_file
            if src.exists():
                shutil.copy2(src, app_dir / icon_file)
        
        # Copy other important files
        other_files = [
            "requirements.txt", "README.md", "live_feed_events.json"
        ]
        
        for file_name in other_files:
            src = PROJECT_ROOT / file_name
            if src.exists():
                shutil.copy2(src, app_dir / file_name)
        
        logger.info("Application files copied successfully")
        return app_dir

    def create_launcher_script(self, app_dir, python_dir):
        """Create launcher script for the application"""
        logger.info("Creating launcher script...")
        
        launcher_content = f'''@echo off
title {APP_NAME} - Social Media Automation Tool
cd /d "%~dp0"

REM Set Python path
set PYTHONPATH=%~dp0app;%~dp0python;%~dp0python\\Lib;%~dp0python\\Lib\\site-packages

REM Start the application
"%~dp0python\\python.exe" "%~dp0app\\main.py"

REM If we get here, the application has closed
if errorlevel 1 (
    echo.
    echo An error occurred. Press any key to close this window.
    pause >nul
)
'''
        
        launcher_path = self.build_dir / f"{APP_NAME}.bat"
        with open(launcher_path, 'w') as f:
            f.write(launcher_content)
        
        logger.info("Launcher script created")
        return launcher_path

    def download_nsis(self):
        """Download and setup NSIS installer"""
        logger.info("Setting up NSIS...")
        
        nsis_url = "https://sourceforge.net/projects/nsis/files/NSIS%203/3.08/nsis-3.08.zip/download"
        nsis_zip = self.temp_dir / "nsis.zip"
        nsis_dir = self.temp_dir / "nsis"
        
        # Download NSIS
        response = requests.get(nsis_url, stream=True, allow_redirects=True)
        response.raise_for_status()
        
        with open(nsis_zip, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        # Extract NSIS
        with zipfile.ZipFile(nsis_zip, 'r') as zip_ref:
            zip_ref.extractall(nsis_dir)
        
        # Find makensis.exe
        makensis_exe = None
        for root, dirs, files in os.walk(nsis_dir):
            if "makensis.exe" in files:
                makensis_exe = Path(root) / "makensis.exe"
                break
        
        if not makensis_exe:
            raise FileNotFoundError("makensis.exe not found in NSIS distribution")
        
        logger.info("NSIS setup completed")
        return makensis_exe

    def create_nsis_script(self, app_dir, python_dir):
        """Create NSIS installer script"""
        logger.info("Creating NSIS installer script...")
        
        nsis_script = f'''
; {APP_NAME} Professional Installer
; Company: {COMPANY_NAME}
; Version: {APP_VERSION}

!define APPNAME "{APP_NAME}"
!define COMPANYNAME "{COMPANY_NAME}"
!define DESCRIPTION "Professional Social Media Automation Tool"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0
!define HELPURL "https://ozmorph.com/support"
!define UPDATEURL "https://ozmorph.com/updates"
!define ABOUTURL "https://ozmorph.com"
!define INSTALLSIZE 500000

RequestExecutionLevel admin
InstallDir "$PROGRAMFILES64\\${{COMPANYNAME}}\\${{APPNAME}}"
LicenseData "license.txt"
Name "${{APPNAME}}"
Icon "app_icon.ico"
outFile "{APP_NAME}_Setup.exe"

!include LogicLib.nsh
!include MUI2.nsh

!define MUI_ABORTWARNING
!define MUI_ICON "app_icon.ico"
!define MUI_UNICON "app_icon.ico"

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "license.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

!insertmacro MUI_LANGUAGE "English"

Section "install"
    SetOutPath $INSTDIR
    
    ; Copy all files
    File /r "{self.build_dir}\\*"
    
    ; Create desktop shortcut
    CreateShortCut "$DESKTOP\\${{APPNAME}}.lnk" "$INSTDIR\\${{APPNAME}}.bat" "" "$INSTDIR\\app\\instagram.ico"
    
    ; Create start menu shortcuts
    CreateDirectory "$SMPROGRAMS\\${{COMPANYNAME}}"
    CreateShortCut "$SMPROGRAMS\\${{COMPANYNAME}}\\${{APPNAME}}.lnk" "$INSTDIR\\${{APPNAME}}.bat" "" "$INSTDIR\\app\\instagram.ico"
    CreateShortCut "$SMPROGRAMS\\${{COMPANYNAME}}\\Uninstall.lnk" "$INSTDIR\\uninstall.exe"
    
    ; Registry entries
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "DisplayName" "${{APPNAME}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "UninstallString" "$\\"$INSTDIR\\uninstall.exe$\\""
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "QuietUninstallString" "$\\"$INSTDIR\\uninstall.exe$\\" /S"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "InstallLocation" "$\\"$INSTDIR$\\""
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "DisplayIcon" "$\\"$INSTDIR\\app\\instagram.ico$\\""
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "Publisher" "${{COMPANYNAME}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "HelpLink" "${{HELPURL}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "URLUpdateInfo" "${{UPDATEURL}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "URLInfoAbout" "${{ABOUTURL}}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "DisplayVersion" "${{VERSIONMAJOR}}.${{VERSIONMINOR}}.${{VERSIONBUILD}}"
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "VersionMajor" ${{VERSIONMAJOR}}
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "VersionMinor" ${{VERSIONMINOR}}
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "NoModify" 1
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "NoRepair" 1
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}" "EstimatedSize" ${{INSTALLSIZE}}
    
    WriteUninstaller "$INSTDIR\\uninstall.exe"
SectionEnd

Section "uninstall"
    Delete "$DESKTOP\\${{APPNAME}}.lnk"
    Delete "$SMPROGRAMS\\${{COMPANYNAME}}\\${{APPNAME}}.lnk"
    Delete "$SMPROGRAMS\\${{COMPANYNAME}}\\Uninstall.lnk"
    RMDir "$SMPROGRAMS\\${{COMPANYNAME}}"
    
    RMDir /r "$INSTDIR"
    
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${{COMPANYNAME}} ${{APPNAME}}"
SectionEnd
'''
        
        script_path = self.installer_dir / "installer.nsi"
        with open(script_path, 'w') as f:
            f.write(nsis_script)
        
        logger.info("NSIS script created")
        return script_path

    def create_license_file(self):
        """Create license agreement file"""
        license_content = f"""END USER LICENSE AGREEMENT

{APP_NAME} - Social Media Automation Tool
Copyright (c) 2024 {COMPANY_NAME}. All rights reserved.

IMPORTANT - READ CAREFULLY: This End User License Agreement ("EULA") is a legal agreement between you (either an individual or a single entity) and {COMPANY_NAME} for the {APP_NAME} software product, which includes computer software and may include associated media, printed materials, and "online" or electronic documentation ("SOFTWARE PRODUCT").

By installing, copying, or otherwise using the SOFTWARE PRODUCT, you agree to be bound by the terms of this EULA. If you do not agree to the terms of this EULA, do not install or use the SOFTWARE PRODUCT.

SOFTWARE PRODUCT LICENSE

The SOFTWARE PRODUCT is protected by copyright laws and international copyright treaties, as well as other intellectual property laws and treaties. The SOFTWARE PRODUCT is licensed, not sold.

1. GRANT OF LICENSE. This EULA grants you the following rights:
   - Installation and Use. You may install and use one copy of the SOFTWARE PRODUCT on a single computer.
   - Backup Copies. You may also make copies of the SOFTWARE PRODUCT as may be necessary for backup and archival purposes.

2. DESCRIPTION OF OTHER RIGHTS AND LIMITATIONS.
   - Limitations on Reverse Engineering, Decompilation, and Disassembly. You may not reverse engineer, decompile, or disassemble the SOFTWARE PRODUCT.
   - Separation of Components. The SOFTWARE PRODUCT is licensed as a single product. Its component parts may not be separated for use on more than one computer.
   - Rental. You may not rent, lease, or lend the SOFTWARE PRODUCT.

3. TERMINATION. Without prejudice to any other rights, {COMPANY_NAME} may terminate this EULA if you fail to comply with the terms and conditions of this EULA.

4. COPYRIGHT. All title and copyrights in and to the SOFTWARE PRODUCT are owned by {COMPANY_NAME}.

5. NO WARRANTIES. {COMPANY_NAME} expressly disclaims any warranty for the SOFTWARE PRODUCT. The SOFTWARE PRODUCT is provided 'As Is' without any express or implied warranty of any kind.

6. LIMITATION OF LIABILITY. In no event shall {COMPANY_NAME} be liable for any damages whatsoever arising out of the use of or inability to use the SOFTWARE PRODUCT.

If you have any questions concerning this EULA, please contact {COMPANY_NAME} at <EMAIL>.
"""

        license_path = self.installer_dir / "license.txt"
        with open(license_path, 'w', encoding='utf-8') as f:
            f.write(license_content)

        return license_path

    def create_app_icon(self):
        """Copy application icon for installer"""
        # Use Instagram icon as the main app icon
        src_icon = PROJECT_ROOT / "instagram.ico"
        dst_icon = self.installer_dir / "app_icon.ico"

        if src_icon.exists():
            shutil.copy2(src_icon, dst_icon)
        else:
            # Create a simple icon if none exists
            logger.warning("No icon found, installer will use default")

        return dst_icon

    def build_installer(self, makensis_exe, nsis_script):
        """Build the final installer"""
        logger.info("Building installer...")

        # Change to installer directory
        original_cwd = os.getcwd()
        os.chdir(self.installer_dir)

        try:
            # Run NSIS compiler
            result = subprocess.run([str(makensis_exe), str(nsis_script)],
                                  capture_output=True, text=True, check=True)

            logger.info("Installer built successfully")
            logger.info(f"NSIS output: {result.stdout}")

            # Find the generated installer
            installer_exe = None
            for file in self.installer_dir.iterdir():
                if file.suffix == '.exe' and 'Setup' in file.name:
                    installer_exe = file
                    break

            if not installer_exe:
                raise FileNotFoundError("Generated installer not found")

            return installer_exe

        except subprocess.CalledProcessError as e:
            logger.error(f"NSIS compilation failed: {e.stderr}")
            raise
        finally:
            os.chdir(original_cwd)

    def copy_to_output(self, installer_exe):
        """Copy final installer to output directory"""
        logger.info("Copying installer to output directory...")

        # Ensure output directory exists
        INSTALLER_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

        # Copy installer
        final_installer = INSTALLER_OUTPUT_DIR / f"{APP_NAME}_Setup_v{APP_VERSION}.exe"
        shutil.copy2(installer_exe, final_installer)

        logger.info(f"Installer created successfully: {final_installer}")
        return final_installer

    def cleanup(self):
        """Clean up temporary files"""
        logger.info("Cleaning up temporary files...")
        try:
            shutil.rmtree(self.temp_dir)
            logger.info("Cleanup completed")
        except Exception as e:
            logger.warning(f"Cleanup failed: {e}")

    def build(self):
        """Main build process"""
        try:
            logger.info("Starting SorcerioModules installer build process...")

            # Step 1: Download Python embeddable
            python_dir = self.download_python_embeddable()

            # Step 2: Install dependencies
            self.install_dependencies(python_dir)

            # Step 3: Copy application files
            app_dir = self.copy_application_files()

            # Step 4: Create launcher script
            self.create_launcher_script(app_dir, python_dir)

            # Step 5: Download and setup NSIS
            makensis_exe = self.download_nsis()

            # Step 6: Create installer resources
            self.create_license_file()
            self.create_app_icon()

            # Step 7: Create NSIS script
            nsis_script = self.create_nsis_script(app_dir, python_dir)

            # Step 8: Build installer
            installer_exe = self.build_installer(makensis_exe, nsis_script)

            # Step 9: Copy to final location
            final_installer = self.copy_to_output(installer_exe)

            logger.info("="*60)
            logger.info("INSTALLER BUILD COMPLETED SUCCESSFULLY!")
            logger.info("="*60)
            logger.info(f"Final installer location: {final_installer}")
            logger.info(f"Installer size: {final_installer.stat().st_size / (1024*1024):.1f} MB")
            logger.info("="*60)

            return final_installer

        except Exception as e:
            logger.error(f"Build failed: {e}")
            raise
        finally:
            # Always cleanup
            self.cleanup()


def main():
    """Main entry point"""
    try:
        builder = SorcerioInstallerBuilder()
        installer_path = builder.build()

        print("\n" + "="*60)
        print("🎉 PROFESSIONAL WINDOWS INSTALLER CREATED SUCCESSFULLY! 🎉")
        print("="*60)
        print(f"📦 Installer: {installer_path}")
        print(f"📁 Location: {INSTALLER_OUTPUT_DIR}")
        print(f"🏢 Company: {COMPANY_NAME}")
        print(f"📱 Application: {APP_NAME}")
        print(f"🔢 Version: {APP_VERSION}")
        print("="*60)
        print("✅ Ready for commercial distribution!")
        print("✅ Zero dependencies required on target machines!")
        print("✅ Professional setup wizard included!")
        print("✅ Automatic desktop and start menu shortcuts!")
        print("✅ Complete uninstaller included!")
        print("="*60)

        return True

    except Exception as e:
        print(f"\n❌ BUILD FAILED: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
