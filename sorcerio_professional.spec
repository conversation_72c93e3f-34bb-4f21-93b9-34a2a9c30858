# -*- mode: python ; coding: utf-8 -*-
"""
Professional PyInstaller spec file for Sorcerio by <PERSON><PERSON><PERSON>
Creates a complete standalone executable with all dependencies bundled
"""

import os
import sys
from pathlib import Path

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define data files to include
datas = [
    # Application files
    ('*.py', '.'),
    ('requirements.txt', '.'),
    
    # Configuration files
    ('configuration', 'configuration'),
    
    # Icons and assets
    ('instagram.ico', '.'),
    ('x.ico', '.'),
    ('ozmorph_logo.png', '.'),
    
    # Portable Chromium (essential for functionality)
    ('portable_chromium', 'portable_chromium'),
    
    # Videos directory structure
    ('videos', 'videos'),
    
    # Live feed thumbnails
    ('live_feed_thumbnails', 'live_feed_thumbnails'),
]

# Hidden imports for all dependencies
hiddenimports = [
    # PyQt5 modules
    'PyQt5.QtCore',
    'PyQt5.QtWidgets', 
    'PyQt5.QtGui',
    'PyQt5.QtWebEngineWidgets',
    'PyQt5.QtWebChannel',
    'PyQt5.QtNetwork',
    'PyQt5.QtPrintSupport',
    
    # Selenium modules
    'selenium',
    'selenium.webdriver',
    'selenium.webdriver.chrome',
    'selenium.webdriver.chrome.service',
    'selenium.webdriver.common.by',
    'selenium.webdriver.support',
    'selenium.webdriver.support.ui',
    'selenium.webdriver.support.expected_conditions',
    'selenium.common.exceptions',
    'undetected_chromedriver',
    
    # Computer vision
    'cv2',
    'numpy',
    
    # Web scraping and requests
    'requests',
    'urllib3',
    'certifi',
    'charset_normalizer',
    'idna',
    
    # JSON and data handling
    'json',
    'pickle',
    'base64',
    
    # System and file operations
    'pathlib',
    'shutil',
    'tempfile',
    'subprocess',
    'threading',
    'queue',
    'time',
    'datetime',
    'logging',
    'os',
    'sys',
    'platform',
    'uuid',
    'itertools',
    'collections',
    'deque',
    
    # Scheduling
    'apscheduler',
    'apscheduler.schedulers.background',
    'apscheduler.triggers.cron',
    'apscheduler.triggers.date',
    'apscheduler.triggers.interval',
    
    # Process management
    'psutil',
    
    # Configuration management
    'appdirs',
    
    # Regex
    're',
    
    # Math and random
    'math',
    'random',
    
    # Network and HTTP
    'socket',
    'ssl',
    'http',
    'http.client',
    'urllib',
    'urllib.parse',
    'urllib.request',
    
    # Encoding
    'encodings',
    'encodings.utf_8',
    'encodings.cp1252',
    'encodings.ascii',
    
    # Additional PyQt5 dependencies
    'sip',
    'PyQt5.sip',
]

# Binaries to include (if any)
binaries = []

# Analysis configuration
a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # Exclude unnecessary modules to reduce size
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'notebook',
        'pytest',
        'unittest',
        'doctest',
        'pdb',
        'profile',
        'cProfile',
        'pstats',
        'trace',
        'timeit',
        'webbrowser',
        'email',
        'xml',
        'xmlrpc',
        'html.parser',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
    ],
    noarchive=False
)

# Remove duplicate files
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Create the executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Sorcerio',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # Compress executable
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='instagram.ico',  # Use Instagram icon as app icon
    version='version_info.py',  # Version information file
)
