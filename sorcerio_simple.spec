# -*- mode: python ; coding: utf-8 -*-
"""
Simplified PyInstaller spec file for Sorcerio by Ozmorph
Creates a complete standalone executable with all dependencies bundled
"""

import os
from pathlib import Path

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Define data files to include
datas = [
    # Configuration files
    ('configuration', 'configuration'),
    
    # Icons and assets
    ('instagram.ico', '.'),
    ('x.ico', '.'),
    ('ozmorph_logo.png', '.'),
    
    # Portable Chromium (essential for functionality)
    ('portable_chromium', 'portable_chromium'),
    
    # Videos directory structure (create empty directories)
    ('videos', 'videos'),
    
    # License file
    ('LICENSE.txt', '.'),
]

# Essential hidden imports only
hiddenimports = [
    # PyQt5 core modules
    'PyQt5.QtCore',
    'PyQt5.QtWidgets', 
    'PyQt5.QtGui',
    'PyQt5.QtWebEngineWidgets',
    'PyQt5.QtWebChannel',
    'PyQt5.sip',
    
    # Selenium essentials
    'selenium.webdriver.chrome.service',
    'selenium.webdriver.common.by',
    'selenium.webdriver.support.ui',
    'selenium.webdriver.support.expected_conditions',
    
    # System modules
    'cv2',
    'numpy',
    'requests',
    'json',
    'logging',
    'threading',
    'subprocess',
    'pathlib',
    'appdirs',
    'psutil',
    
    # Scheduling
    'apscheduler.schedulers.background',
    'apscheduler.triggers.cron',
]

# Analysis configuration
a = Analysis(
    ['main.py'],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # Exclude problematic modules
        'tkinter',
        'matplotlib',
        'scipy',
        'pandas',
        'jupyter',
        'IPython',
        'notebook',
        'pytest',
        'unittest',
        'doctest',
        'pdb',
        'profile',
        'cProfile',
        'pstats',
        'trace',
        'timeit',
        'webbrowser',
        'email',
        'xml',
        'xmlrpc',
        'html.parser',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
        'yt_dlp',  # Exclude problematic yt-dlp
        'instagrapi',  # Exclude problematic instagrapi
        'instaloader',  # Exclude problematic instaloader
    ],
    noarchive=False,
)

# Remove duplicate files
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Create the executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Sorcerio',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # Compress executable
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='instagram.ico',  # Use Instagram icon as app icon
)
