#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SorcerioModules Installer Verification Script
Tests the created installer to ensure all components work correctly.
"""

import os
import sys
import zipfile
import tempfile
import shutil
from pathlib import Path

def test_installer():
    """Test the created installer"""
    installer_path = Path("C:/Users/<USER>/Desktop/sorcsetup/Sorcerio_Professional_Installer_v1.0.0.zip")
    
    print("🔍 Testing SorcerioModules Professional Installer")
    print("=" * 60)
    
    # Check if installer exists
    if not installer_path.exists():
        print("❌ Installer file not found!")
        return False
    
    print(f"✅ Installer found: {installer_path}")
    print(f"📦 Size: {installer_path.stat().st_size / (1024*1024):.1f} MB")
    
    # Test ZIP extraction
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"🔄 Testing ZIP extraction to: {temp_dir}")
            
            with zipfile.ZipFile(installer_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # Check extracted contents
            extracted_files = list(Path(temp_dir).rglob("*"))
            print(f"✅ Extracted {len(extracted_files)} files/folders")
            
            # Check for key files
            key_files = [
                "install.bat",
                "README.txt",
                "SorcerioApp/Sorcerio.vbs",  # VBS launcher for no console
                "SorcerioApp/Sorcerio.py",   # Python launcher
                "SorcerioApp/Sorcerio.bat",  # Batch fallback
                "SorcerioApp/app/main.py",
                "SorcerioApp/app/ui.py",
                "SorcerioApp/app/download.py",
                "SorcerioApp/app/upload.py",
                "SorcerioApp/app/utils.py",
                "SorcerioApp/app/stats.py",
                "SorcerioApp/app/requirements.txt"
            ]
            
            missing_files = []
            for key_file in key_files:
                file_path = Path(temp_dir) / key_file
                if file_path.exists():
                    print(f"✅ {key_file}")
                else:
                    print(f"❌ {key_file} - MISSING")
                    missing_files.append(key_file)
            
            if missing_files:
                print(f"\n❌ Missing {len(missing_files)} critical files!")
                return False
            
            # Test Python import syntax
            print("\n🔄 Testing Python syntax...")
            main_py = Path(temp_dir) / "SorcerioApp/app/main.py"
            if main_py.exists():
                try:
                    with open(main_py, 'r', encoding='utf-8') as f:
                        content = f.read()
                    compile(content, str(main_py), 'exec')
                    print("✅ main.py syntax is valid")
                except SyntaxError as e:
                    print(f"❌ main.py syntax error: {e}")
                    return False
            
            # Test utils.py imports
            utils_py = Path(temp_dir) / "SorcerioApp/app/utils.py"
            if utils_py.exists():
                try:
                    with open(utils_py, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check for required functions
                    required_functions = [
                        "def resource_path",
                        "def get_logo_path",
                        "def detect_platform_key",
                        "def update_feed_event",
                        "def get_skeleton_html",
                        "def format_live_feed_event",
                        "def reload_scheduler_helper",
                        "feed_emitter",
                        "live_feed_manager",
                        "TELEGRAM_BOT_TOKEN",
                        "TELEGRAM_CHAT_ID"
                    ]
                    
                    for func in required_functions:
                        if func in content:
                            print(f"✅ {func} found in utils.py")
                        else:
                            print(f"❌ {func} missing from utils.py")
                            return False
                            
                except Exception as e:
                    print(f"❌ Error reading utils.py: {e}")
                    return False
            
            print("\n✅ All tests passed!")
            print("🎉 Installer is ready for distribution!")
            return True
            
    except Exception as e:
        print(f"❌ Error testing installer: {e}")
        return False


def main():
    """Main test function"""
    print("SorcerioModules Professional Installer Test")
    print("=" * 50)
    
    success = test_installer()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 INSTALLER VERIFICATION SUCCESSFUL! 🎉")
        print("=" * 60)
        print("✅ All components verified")
        print("✅ Python syntax validated")
        print("✅ Required functions present")
        print("✅ Ready for commercial distribution")
        print("=" * 60)
        return True
    else:
        print("\n" + "=" * 60)
        print("❌ INSTALLER VERIFICATION FAILED!")
        print("=" * 60)
        print("Please check the errors above and rebuild the installer.")
        print("=" * 60)
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
