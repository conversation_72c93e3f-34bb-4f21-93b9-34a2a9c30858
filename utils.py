import json
import logging
import os
import re
import shutil
import types
from datetime import datetime
import time
import itertools
import uuid
import sys
from pathlib import Path
import appdirs # appdirs importu eklendi

import cv2
import numpy as np

# Chrome driver yönetimi için yeni importlar
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
import threading
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

# Chrome driver yönetimi için global lock
_chrome_driver_lock = threading.Lock()

# Uygulama ve Yazar Bilgileri (appdirs için)
APP_NAME = "Sorcerio"
APP_AUTHOR = "Ozmorph"

# Kullanıcıya Özel Dizinler
USER_CONFIG_DIR = Path(appdirs.user_config_dir(APP_NAME, APP_AUTHOR))
USER_DATA_DIR = Path(appdirs.user_data_dir(APP_NAME, APP_AUTHOR))
USER_LOG_DIR = Path(appdirs.user_log_dir(APP_NAME, APP_AUTHOR))

def _ensure_user_file_or_copy_default(user_file_path: Path, resource_relative_path: str | None) -> Path:
    """
    Belirtilen kullanıcı dosya yolunun var olduğundan emin olur.
    Eğer dosya yoksa ve bir kaynak yolu (resource_relative_path) belirtilmişse,
    paketlenmiş kaynaktan bu dosyayı kullanıcı yoluna kopyalar.
    Kullanıcı dosya yolunun üst dizinlerini oluşturur.
    Son kullanıcı dosya yolunu döndürür.
    """
    try:
        # Kullanıcı dosyasının bulunacağı dizini oluştur (varsa dokunmaz)
        user_file_path.parent.mkdir(parents=True, exist_ok=True)

        if not user_file_path.exists() and resource_relative_path:
            default_resource_abs_path = resource_path(resource_relative_path)
            if os.path.exists(default_resource_abs_path):
                shutil.copy2(default_resource_abs_path, user_file_path)
                logging.info(f"Varsayılan kaynak '{default_resource_abs_path}' kullanıcı dizinine kopyalandı: '{user_file_path}'")
            else:
                # Varsayılan kaynak yoksa (örn: dosya ilk çalıştırmada oluşturulacaksa), sadece logla.
                logging.info(f"Varsayılan kaynak '{resource_relative_path}' bulunamadı. '{user_file_path}' sıfırdan oluşturulacak (eğer gerekiyorsa)." )
        return user_file_path
    except Exception as e:
        logging.error(f"Kullanıcı dosyası ({user_file_path}) ayarlanırken veya varsayılan kaynak kopyalanırken hata: {e}", exc_info=True)
        # Hata durumunda bile yolu döndürmeye çalış, çağıran kod belki yine de işleyebilir
        # veya en azından yolun ne olması gerektiğini bilir.
        return user_file_path 

def _cleanup_chrome_profile(profile_path, account_username):
    """Chrome profil klasörünü güvenli şekilde temizler (hızlı versiyon)"""
    try:
        if os.path.exists(profile_path):
            # Önce Chrome işlemlerini kapat
            _kill_chrome_processes(profile_path)

            # Sadece kritik dosyaları sil (hızlı temizlik)
            critical_files = [
                "DevToolsActivePort",
                "SingletonLock",
                "SingletonSocket",
                "SingletonCookie",
                "LOCK",
                "chrome_debug.log"
            ]

            for critical_file in critical_files:
                file_path = os.path.join(profile_path, critical_file)
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        logging.debug(f"Kritik dosya silindi: {critical_file}")
                    except Exception as e:
                        logging.debug(f"Kritik dosya silinemedi ({critical_file}): {e}")

            # Crash dump klasörlerini temizle
            crash_dirs = ["Crashpad", "crash_dumps"]
            for crash_dir in crash_dirs:
                crash_path = os.path.join(profile_path, crash_dir)
                if os.path.exists(crash_path):
                    try:
                        import shutil
                        shutil.rmtree(crash_path, ignore_errors=True)
                        logging.debug(f"Crash klasörü silindi: {crash_dir}")
                    except Exception as e:
                        logging.debug(f"Crash klasörü silinemedi ({crash_dir}): {e}")

            logging.debug(f"Chrome profil klasörü temizlendi: {profile_path}")

    except Exception as e:
        logging.warning(f"Chrome profil temizleme hatası: {e}")

def _kill_chrome_processes(profile_path):
    """Belirli profil klasörünü kullanan Chrome işlemlerini kapatır"""
    try:
        import psutil
        killed_count = 0

        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if profile_path in cmdline:
                        proc.terminate()
                        try:
                            proc.wait(timeout=5)
                            killed_count += 1
                            logging.debug(f"Chrome işlemi kapatıldı: PID {proc.info['pid']}")
                        except psutil.TimeoutExpired:
                            proc.kill()
                            killed_count += 1
                            logging.debug(f"Chrome işlemi zorla kapatıldı: PID {proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if killed_count > 0:
            logging.info(f"{killed_count} Chrome işlemi kapatıldı")
            time.sleep(2)  # İşlemlerin tamamen kapanması için bekle

    except ImportError:
        logging.warning("psutil modülü bulunamadı, Chrome işlemleri kapatılamadı")
    except Exception as e:
        logging.warning(f"Chrome işlemleri kapatılırken hata: {e}")

def _create_robust_chrome_options(profile_path, headless=True):
    """Güçlendirilmiş Chrome options oluşturur"""
    options = webdriver.ChromeOptions()

    # Temel headless ayarları
    if headless:
        options.add_argument('--headless=new')

    # Crash önleme parametreleri
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--disable-software-rasterizer')
    options.add_argument('--disable-background-timer-throttling')
    options.add_argument('--disable-backgrounding-occluded-windows')
    options.add_argument('--disable-renderer-backgrounding')
    options.add_argument('--disable-features=TranslateUI,VizDisplayCompositor')
    options.add_argument('--disable-ipc-flooding-protection')
    options.add_argument('--disable-hang-monitor')
    options.add_argument('--disable-client-side-phishing-detection')
    options.add_argument('--disable-popup-blocking')
    options.add_argument('--disable-prompt-on-repost')
    options.add_argument('--disable-sync')
    options.add_argument('--disable-extensions')
    options.add_argument('--disable-plugins')
    options.add_argument('--disable-images')
    # JavaScript'i devre dışı bırakma - Twitter için gerekli
    # options.add_argument('--disable-javascript')
    options.add_argument('--disable-notifications')
    options.add_argument('--disable-web-security')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-logging')
    options.add_argument('--disable-default-apps')
    options.add_argument('--disable-background-networking')

    # Performans ve stabilite
    options.add_argument('--max_old_space_size=4096')
    options.add_argument('--memory-pressure-off')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--lang=en-US')
    options.add_argument('--force-color-profile=srgb')
    options.add_argument('--metrics-recording-only')
    options.add_argument('--use-mock-keychain')

    # Profil klasörü
    options.add_argument(f'--user-data-dir={profile_path}')

    # Remote debugging port (0 = otomatik port seçimi)
    options.add_argument('--remote-debugging-port=0')

    # Ek crash önleme parametreleri (sadece kritik olanlar)
    options.add_argument('--disable-crash-reporter')
    options.add_argument('--disable-logging')
    options.add_argument('--log-level=3')  # Sadece fatal error'ları logla
    options.add_argument('--silent')
    options.add_argument('--disable-breakpad')
    options.add_argument('--disable-component-update')
    options.add_argument('--disable-domain-reliability')
    options.add_argument('--disable-field-trial-config')

    # Preferences
    prefs = {
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0,
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.media_stream": 2,
        "profile.default_content_setting_values.geolocation": 2,
        "profile.managed_default_content_settings.media_stream": 2
    }
    options.add_experimental_option("prefs", prefs)

    # Diğer experimental options
    options.add_experimental_option("useAutomationExtension", False)
    options.add_experimental_option("excludeSwitches", ["enable-automation", "enable-logging"])

    return options

def create_chrome_driver_for_account(account_username, headless=True, max_retries=3):
    """
    Sadece portable Chromium + uygun Chromedriver ile başlatır.
    Sistem Chrome'unu ve diğer tüm yolları yok sayar.
    Kapsamlı hata yönetimi ve retry mekanizması ile.
    """
    last_error = None

    for attempt in range(max_retries):
        try:
            with _chrome_driver_lock:
                # Her denemede profil klasörünü temizle (ilk denemede de!)
                base_dir = os.path.dirname(os.path.abspath(__file__))
                profile_base = os.path.join(base_dir, f"chrome_profile_{account_username}")

                # İlk denemede de profil klasörünü temizle - bu crash'i önler
                _cleanup_chrome_profile(profile_base, account_username)

                os.makedirs(profile_base, exist_ok=True)

                # Chrome options'ı güçlendir
                options = _create_robust_chrome_options(profile_base, headless)

                # Portable Chromium binary ve chromedriver yolunu al
                from download import setup_portable_chromium, prepare_unique_chromedriver
                binary_path, driver_path = setup_portable_chromium()

                # Binary dosyasının var olduğunu kontrol et
                if not os.path.exists(binary_path):
                    raise Exception(f"Chrome binary bulunamadı: {binary_path}")

                options.binary_location = binary_path

                # Kilitlenme sorununu önlemek için tek kullanımlık kopya
                driver_path = prepare_unique_chromedriver(driver_path)

                # Service'i güçlendir
                service = Service(
                    driver_path,
                    log_path=os.path.join(profile_base, "chromedriver.log"),
                    service_args=['--verbose']
                )

                # Driver'ı oluştur (timeout ile)
                import signal

                def timeout_handler(signum, frame):
                    raise TimeoutError("Chrome driver oluşturma timeout")

                # Windows'ta signal.alarm çalışmaz, alternatif yöntem kullan
                try:
                    driver = webdriver.Chrome(service=service, options=options)
                except Exception as e:
                    if "timeout" in str(e).lower() or "devtoolsactiveport" in str(e).lower():
                        raise Exception(f"Chrome başlatma hatası: {e}")
                    raise e

                # Driver'ın düzgün çalıştığını test et
                driver.set_page_load_timeout(30)
                driver.implicitly_wait(10)

                # Health check - basit bir sayfa yükle
                try:
                    driver.get("data:text/html,<html><body>Chrome Test</body></html>")
                    if "Chrome Test" not in driver.page_source:
                        raise Exception("Chrome health check başarısız")
                except Exception as e:
                    driver.quit()
                    raise Exception(f"Chrome health check hatası: {e}")

                logging.info(f"Portable Chrome driver başarıyla oluşturuldu: {account_username} (Deneme: {attempt + 1})")
                return driver

        except Exception as e:
            last_error = e
            error_msg = str(e)
            logging.warning(f"Chrome driver oluşturma hatası (Deneme {attempt + 1}/{max_retries}): {error_msg}")

            # Özel hata durumları için ek işlemler
            if "DevToolsActivePort" in error_msg or "crashed" in error_msg:
                logging.warning("DevToolsActivePort hatası tespit edildi, profil temizleniyor...")
                _cleanup_chrome_profile(profile_base, account_username)

            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 2  # Exponential backoff
                logging.info(f"Chrome driver yeniden denenecek, bekleniyor: {wait_time} saniye...")
                time.sleep(wait_time)
            else:
                logging.error(f"Chrome driver oluşturulamadı, tüm denemeler başarısız: {account_username}")

    # Tüm denemeler başarısız oldu
    raise Exception(f"Chrome driver oluşturulamadı ({max_retries} deneme sonrası): {last_error}")

def create_new_driver_for_account(account_username):
    """
    Eski kodla uyumluluk için create_chrome_driver_for_account'a yönlendiren wrapper fonksiyon.
    Portable Chromium kullanır ve crash sorunlarını önler.
    """
    try:
        return create_chrome_driver_for_account(account_username, headless=True, max_retries=3)
    except Exception as e:
        logging.error(f"create_new_driver_for_account hatası ({account_username}): {e}")
        raise e

def setup_driver():
    """
    Twitter statistics için driver oluşturur.
    Eski kodla uyumluluk için portable Chromium kullanır.
    """
    try:
        return create_chrome_driver_for_account("twitter_stats", headless=True, max_retries=3)
    except Exception as e:
        logging.error(f"setup_driver hatası: {e}")
        raise e

def safe_quit_driver(driver):
    """Chrome driver'ı güvenli şekilde kapatır"""
    if driver:
        try:
            # Önce tüm window'ları kapat
            for handle in driver.window_handles:
                try:
                    driver.switch_to.window(handle)
                    driver.close()
                except Exception:
                    pass

            # Driver'ı kapat
            driver.quit()

            # Biraz bekle
            time.sleep(1)

        except Exception as e:
            logging.debug(f"Driver kapatma hatası: {e}")
            try:
                # Force quit
                driver.service.process.terminate()
                time.sleep(1)
                driver.service.process.kill()
            except Exception:
                pass

def cleanup_all_chrome_profiles_on_startup():
    """Program başlangıcında tüm Chrome profil klasörlerini temizler"""
    try:
        base_dir = os.path.dirname(os.path.abspath(__file__))

        # Tüm Chrome işlemlerini kapat
        logging.info("Program başlangıcında Chrome işlemleri kapatılıyor...")
        try:
            import psutil
            killed_count = 0

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                        if 'chrome_profile_' in cmdline or 'portable_chromium' in cmdline:
                            proc.terminate()
                            try:
                                proc.wait(timeout=3)
                                killed_count += 1
                            except psutil.TimeoutExpired:
                                proc.kill()
                                killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if killed_count > 0:
                logging.info(f"Program başlangıcında {killed_count} Chrome işlemi kapatıldı")
                time.sleep(2)

        except ImportError:
            logging.warning("psutil modülü bulunamadı, Chrome işlemleri kapatılamadı")
        except Exception as e:
            logging.warning(f"Chrome işlemleri kapatılırken hata: {e}")

        # Chrome profil klasörlerini bul ve temizle
        profile_dirs = []
        for item in os.listdir(base_dir):
            if item.startswith('chrome_profile_') and os.path.isdir(os.path.join(base_dir, item)):
                profile_dirs.append(item)

        if profile_dirs:
            logging.info(f"Program başlangıcında {len(profile_dirs)} Chrome profil klasörü temizleniyor...")

            for profile_dir in profile_dirs:
                profile_path = os.path.join(base_dir, profile_dir)
                username = profile_dir.replace('chrome_profile_', '')

                try:
                    _cleanup_chrome_profile(profile_path, username)
                    logging.debug(f"Chrome profil klasörü temizlendi: {profile_dir}")
                except Exception as e:
                    logging.warning(f"Chrome profil klasörü temizlenemedi ({profile_dir}): {e}")

            logging.info("Chrome profil klasörleri temizleme işlemi tamamlandı")
        else:
            logging.debug("Temizlenecek Chrome profil klasörü bulunamadı")

    except Exception as e:
        logging.error(f"Chrome profil klasörleri temizleme hatası: {e}")

def dummy_duration(path):
    """
    Get video duration using OpenCV with improved accuracy and fallback methods.
    """
    try:
        cap = cv2.VideoCapture(path)
        if not cap.isOpened():
            # Try alternative method with FFprobe if available
            return get_duration_with_ffprobe(path)

        # Method 1: Use CAP_PROP_POS_MSEC for more accurate duration
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)

        # Try to get duration directly from video properties
        duration_ms = cap.get(cv2.CAP_PROP_POS_MSEC)

        # Calculate duration using frame count and FPS
        if fps > 0 and frame_count > 0:
            calculated_duration = frame_count / fps
        else:
            calculated_duration = 30  # Default fallback

        cap.release()

        # Return the calculated duration (more reliable than direct property)
        return max(calculated_duration, 1.0)  # Ensure minimum 1 second

    except Exception as e:
        logging.debug(f"OpenCV duration detection failed for {path}: {e}")
        # Fallback to FFprobe if available
        return get_duration_with_ffprobe(path)


def get_duration_with_ffprobe(path):
    """
    Fallback method to get video duration using FFprobe if available.
    """
    try:
        import subprocess
        from download import download_ffmpeg

        ffmpeg_dir = download_ffmpeg()
        if not ffmpeg_dir:
            return 30  # Default fallback

        ffprobe_exe = os.path.join(ffmpeg_dir, "ffprobe.exe")
        if not os.path.exists(ffprobe_exe):
            return 30  # Default fallback

        cmd = [
            ffprobe_exe,
            "-v", "quiet",
            "-show_entries", "format=duration",
            "-of", "csv=p=0",
            path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and result.stdout.strip():
            duration = float(result.stdout.strip())
            return max(duration, 1.0)  # Ensure minimum 1 second

    except Exception as e:
        logging.debug(f"FFprobe duration detection failed for {path}: {e}")

    return 30  # Final fallback


def setup_logging() -> logging.Logger:
    """Loglama ayarlarını yapar ve log dosyasını kullanıcı log dizinine kaydeder."""
    # USER_LOG_DIR global değişkeni dosyanın başında tanımlanmış olmalı
    log_dir = USER_LOG_DIR
    log_file_name = "sorcerio_activity.log"
    
    try:
        log_dir.mkdir(parents=True, exist_ok=True)
    except OSError as e:
        # Log dizini oluşturulamazsa, geçici bir dizine veya proje kök dizinine loglamayı deneyebiliriz.
        # Şimdilik sadece bir uyarı verip devam edelim, loglama konsola yapılmaya devam edecektir.
        print(f"UYARI: Log dizini ({log_dir}) oluşturulamadı: {e}. Loglar sadece konsola yazdırılacak.")
        # Veya fallback olarak mevcut çalışma dizinini kullan:
        # log_dir = Path(".") 
    
    log_file_path = log_dir / log_file_name

    # Temel logger konfigürasyonu
    # Not: logging.basicConfig birden fazla çağrılırsa etkisiz kalabilir.
    # Bu yüzden bir 'root' logger alıp ona handler eklemek daha esnek olabilir.
    # Ancak mevcut yapı buysa, dosya yolunu güncellemek yeterli.

    # Kök logger'ı al
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG) # Ana log seviyesini ayarla

    # Mevcut handler'ları temizle (eğer varsa, tekrar tekrar eklenmemesi için)
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
        handler.close()

    # Konsol handler'ı
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO) # Konsola INFO ve üzeri loglar
    console_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(module)s - %(message)s")
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # Dosya handler'ı (RotatingFileHandler ile log dosyasının aşırı büyümesini engelle)
    try:
        from logging.handlers import RotatingFileHandler
        # 5MB boyutunda 3 yedek dosya sakla
        file_handler = RotatingFileHandler(log_file_path, maxBytes=5*1024*1024, backupCount=3, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG) # Dosyaya DEBUG ve üzeri tüm loglar
        file_formatter = logging.Formatter("%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(module)s - %(funcName)s - %(message)s")
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        logger.info(f"Loglama başlatıldı. Log dosyası: {log_file_path}")
    except Exception as e:
        logger.error(f"Log dosyası handler'ı oluşturulamadı ({log_file_path}): {e}. Loglar sadece konsola yazılacak.", exc_info=True)

    # Selenium loglarını ayıkla (çok konuşkan olabilirler)
    logging.getLogger("selenium").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("undetected_chromedriver").setLevel(logging.WARNING)

    return logger

def create_directory(path):
    """İndirilen dosyaların kaydedileceği dizini oluşturur"""
    if not os.path.exists(path):
        os.makedirs(path)
        logging.info(f"Dizin oluşturuldu: {path}")


def clean_temp_files(temp_dir, retries=5):
    for attempt in range(retries):
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                logging.info(f"Geçici dosyalar temizlendi: {temp_dir}")
                return
        except Exception as e:
            logging.error(f"Geçici dosyalar temizlenirken hata oluştu (deneme {attempt+1}): {str(e)}")
            time.sleep(0.5)
    logging.error(f"Geçici dosya silinemedi: {temp_dir}")


def protect_media_files(profile_json_path):
    """
    Henüz paylaşılmamış medya dosyalarını koruma listesine alır.
    JSON'daki tüm medya dosyalarını ve thumbnail'lerini korur.
    Shortcode-based naming için özel koruma sağlar.
    """
    protected_files = set()

    try:
        if os.path.exists(profile_json_path):
            with open(profile_json_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            # Downloaded listesindeki tüm dosyaları koru
            for item in data.get("downloaded", []):
                if isinstance(item, dict):
                    # Legacy file_path support
                    if "file_path" in item:
                        file_path = item["file_path"]
                        if file_path and os.path.exists(file_path):
                            protected_files.add(file_path)

                            # Video dosyası için thumbnail'i de koru
                            if file_path.lower().endswith('.mp4'):
                                thumbnail_path = file_path.rsplit('.', 1)[0] + '.jpg'
                                if os.path.exists(thumbnail_path):
                                    protected_files.add(thumbnail_path)

                    # New shortcode-based paths
                    video_path = item.get("video_path")
                    if video_path and os.path.exists(video_path):
                        protected_files.add(video_path)

                    thumbnail_path = item.get("thumbnail_path")
                    if thumbnail_path and os.path.exists(thumbnail_path):
                        protected_files.add(thumbnail_path)

                    # For shortcode-based files, protect both video and thumbnail
                    shortcode = item.get("shortcode")
                    if shortcode:
                        # Get the directory from existing file paths or use default
                        base_dir = None
                        if video_path:
                            base_dir = os.path.dirname(video_path)
                        elif thumbnail_path:
                            base_dir = os.path.dirname(thumbnail_path)
                        elif "file_path" in item and item["file_path"]:
                            base_dir = os.path.dirname(item["file_path"])

                        if base_dir:
                            # Protect shortcode-based files
                            shortcode_video = os.path.join(base_dir, f"{shortcode}.mp4")
                            shortcode_thumbnail = os.path.join(base_dir, f"{shortcode}.jpg")

                            if os.path.exists(shortcode_video):
                                protected_files.add(shortcode_video)
                            if os.path.exists(shortcode_thumbnail):
                                protected_files.add(shortcode_thumbnail)

    except Exception as e:
        logging.warning(f"Medya koruma listesi oluşturulurken hata: {e}")

    return protected_files


def safe_file_delete(file_path, protected_files=None, allow_deletion=False):
    """
    Dosyayı güvenli bir şekilde siler. Korunan dosyalar silinmez.

    Args:
        file_path: Silinecek dosya yolu
        protected_files: Korunan dosyalar seti
        allow_deletion: True ise silme işlemine izin verir (post-sharing cleanup için)

    Returns:
        bool: Silme işlemi başarılı ise True
    """
    if protected_files is None:
        protected_files = set()

    # Absolute protection: Never delete Instagram/Twitter media files unless explicitly allowed
    if not allow_deletion:
        # Check if this is a media file that should be protected
        if (file_path.lower().endswith(('.mp4', '.jpg', '.jpeg', '.png', '.webm')) and
            ('instagramdownloaded' in file_path or 'twitterdownloaded' in file_path)):
            logging.debug(f"Media file protected from accidental deletion: {file_path}")
            return False

    if file_path in protected_files:
        logging.debug(f"Korunan dosya silinmedi: {file_path}")
        return False

    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logging.debug(f"Dosya güvenli bir şekilde silindi: {file_path}")
            return True
    except Exception as e:
        logging.warning(f"Dosya silinirken hata: {file_path} - {e}")

    return False


# Modül seviyesinde sayaç
_filename_counter = itertools.count(1)

def sanitize_filename(filename: str) -> str:
    """
    100 yıl sorunsuz çalışacak, tamamen sayısal + güvenli ASCII ad üretir.

    Çıktı ► YYYYMMDD-HHMMSS-NNNN [-kisa-anahtar]
        • tarih-saat : sıralanabilir
        • NNNN      : aynı saniye içinde bile benzersiz
        • kisa-anahtar (opsiyonel) : ilk 1 kelime ASCII-leştirilmiş özet (kısaltıldı)

    Dosya adı kısaltılır, yasak karakterler temizlenir.
    Windows'un 260 karakter sınırını aşmamak için daha kısa isimler üretir.
    """
    if not filename:
        filename = "file"

    # temel ascii özet
    ascii_name = (
        filename.encode("ascii", "ignore").decode()
        .replace(" ", "-")
        .replace("_", "-")
    )
    ascii_name = re.sub(r"[<>:\"/\\|?*#]", "", ascii_name)

    # Sadece ilk kelimeyi al ve maksimum 10 karakter ile sınırla
    words = re.findall(r"[A-Za-z0-9\-]+", ascii_name)
    if words:
        # Sadece ilk kelimeyi al ve 10 karakterle sınırla
        short_part = words[0][:10].lower()
    else:
        short_part = ""

    # Daha kısa zaman damgası formatı (yıl son 2 rakam)
    timestamp = datetime.now().strftime("%y%m%d-%H%M%S")
    unique_no = next(_filename_counter)
    core = f"{timestamp}-{unique_no:04d}"

    return f"{core}-{short_part}" if short_part else core


def create_video_thumbnail(video_path: str,
                           thumbnail_path: str | None = None,
                           wait_seconds: int = 30) -> str:
    """
    Video tamamlanmadan çağrılmışsa bile thumbnail üretmeyi dener.
    • .part uzantısını otomatik sıyırır
    • video dosyasının oluşmasını max `wait_seconds` bekler
    • var olan thumbnail'i asla ezmez
    """
    # .part ile bitiyorsa gerçek adı tahmin et
    if video_path.endswith(".part"):
        video_path = video_path[:-5]

    if not thumbnail_path:
        thumbnail_path = os.path.splitext(video_path)[0] + ".jpg"

    # Thumbnail zaten varsa
    if os.path.exists(thumbnail_path):
        return thumbnail_path

    # Video henüz flush edilmemiş olabilir → bekle
    timeout = time.time() + wait_seconds
    while not os.path.exists(video_path):
        if time.time() > timeout:
            logging.error(f"Thumbnail beklerken video bulunamadı: {video_path}")
            return thumbnail_path
        time.sleep(1)

    try:
        cap = cv2.VideoCapture(video_path)
        # Bazen video yazma tamamlandıktan hemen sonra açılmaz
        if not cap.isOpened():
            time.sleep(1)
            cap.open(video_path)

        ret, frame = cap.read()
        cap.release()
        if not ret:
            raise RuntimeError("İlk kare okunamadı")

        cv2.imwrite(thumbnail_path, frame)
        logging.info(f"Thumbnail oluşturuldu: {thumbnail_path}")
    except Exception as e:
        logging.error(f"Thumbnail oluşturulamadı ({video_path}): {e}")

    return thumbnail_path


html_content = r"""
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>3 Deck UI - Dark Theme (Modernized)</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
    .skeleton {
    background: linear-gradient(-90deg, #2b2b2b 0%, #3a3a3a 50%, #2b2b2b 100%);
    background-size: 400% 400%;
    animation: shimmer 1.2s ease-in-out infinite;
    border-radius: 10px;
    height: 20px;
    width: 80%;
    margin: 6px auto;
}


@keyframes shimmer {
    0% { background-position: 100% 0; }
    100% { background-position: -100% 0; }
}

.skeleton-container {
    display: flex;
    flex-direction: column;
    gap: 14px;
    padding: 10px 5px;
}


    /* ==== GENEL KUTU DİZİLİMİ ==== */
    .stats-grid {
    display: grid;
    /* Genişliğe göre otomatik kolon sayısı (120 px'den küçükse tek sütuna iner) */
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.stat-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1e1e1e;
    padding: 10px 15px;
    border-radius: 10px;
}



    /* ==== TWITTER KUTULARI ==== */
    .twitter-stats .stat-box {
        width: 100%;                    /* Hücrenin tamamını kapsar */
        height: auto;
        font-size: 13px;
        padding: 10px;
        background-color: #1da1f2;
        color: white;
        border-radius: 10px;
        text-align: center;
        box-sizing: border-box;         /* İçerik dışarı taşmasın */
    }


    .instagram-stats .stat-box {
        width: 100%;
        height: auto;
        font-size: 13px;               /* Aynı font boyutu */
        padding: 10px;                 /* Twitter'da 10, Instagram'da 10px 12px idi => eşitledik */
        background-color: #1E1E1E;     /* İsterseniz #1da1f2 yapıp aynı renge getirebilirsiniz */
        color: #FFFFFF;
        border-radius: 10px;
        text-align: center;            /* Twitter'daki gibi ortalarsanız aynı görünür */
        box-sizing: border-box;
    }




    .stat-label {
        font-size: 14px;
        color: #ccc;
    }

    .stat-right {
        text-align: right;
    }

    .stat-value {
        font-size: 18px;
        font-weight: bold;
        color: #fff;
    }

    .instagram-stats .stat-label {
        font-size: 14px;
        color: #ccc;
        text-align: left;
    }
    .instagram-stats .stat-value {
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        text-align: left;
    }



    .stat-change {
        font-size: 13px;
        margin-top: 2px;
    }



    .stat-change.up {
        color: #00ff88;
    }

    .stat-change.down {
        color: #ff5555;
    }
        :root {
            --bg-color: #121212;
            --card-bg: #1E1E1E;
            --text-primary: #FFFFFF;
            --text-secondary: #B3B3B3;
            --accent-blue: #2196F3;
            --border-color: #373737;
            --base-font-size: 14px;
            --section-header-size: 13px;
            --deck-padding: 12px;
            --gap-size: 10px;
            --title-bar-height: 36px;

            /* 1) Üst ve alt bar rengini biraz daha açtık (örnek: #3C3C3C) */
            --header-footer-bg: #3C3C3C;

            /* DPI duyarlı boyutlar için rem birimleri */
            --base-rem-size: 1rem;
            --small-rem-size: 0.875rem;
            --large-rem-size: 1.125rem;
        }
        html {
            /* Temel font boyutu - DPI ölçeklendirmesi için */
            font-size: 14px;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            background-color: var(--bg-color);
            font-family: 'Segoe UI', sans-serif;
            color: var(--text-primary);
            overflow: hidden;
            /* Rem tabanlı font boyutu */
            font-size: var(--base-rem-size);
        }

        .title-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--title-bar-height);
            background-color: var(--header-footer-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            z-index: 999;
            -webkit-user-select: none;
            cursor: default;
        }

        .status-indicator-bar {
            position: absolute;
            top: 9px; /* ¡¡¡ Aşağıya indirildi */
            left: 50%;
            transform: translateX(-50%);
            width: 100px;  /* ? Daha geniş */
            height: 20px;  /* ^ Daha yüksek */
            background-color: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            z-index: 9999;
        }

        .status-light-bar {
            width: 100px;
            height: 20px;
            border-radius: 10px;
            box-shadow: 0 0 4px #00000055;
            background-size: 200% 100%;
        }



        @keyframes idleAnim {
            0% { background-position: 100% 0; }
            100% { background-position: -100% 0; }
        }

        @keyframes activeAnim {
            0% { background-position: 0% 50%; background-color: #2ecc71; }
            100% { background-position: 100% 50%; background-color: #27ae60; }
        }

        @keyframes stopAnim {
            0% { background-position: 0% 50%; background-color: #e74c3c; }
            100% { background-position: 100% 50%; background-color: #c0392b; }
        }

        /* YENİ EKLENEN - Akış animasyonları */
        @keyframes flowGreen {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        @keyframes flowRed {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        /* --- YENİ EKLENEN SONU --- */





        .window-title {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg,
                #FFD700 0%,
                #FFD700 45%,
                #FFFFFF 45%,
                #FFFFFF 55%,
                #FFD700 55%,
                #FFD700 100%
            );
            background-size: 200%;
            background-repeat: repeat;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: shine 3s infinite alternate;
        }



        @keyframes shine {
            0% {
                background-position: 0% 0;
                animation-timing-function: linear;
            }
            80% {
                background-position: 90% 0;
                animation-timing-function: ease-out;
            }
            100% {
                background-position: 100% 0;
            }
        }

        .window-buttons {
            display: flex;
            gap: 6px;
        }
        .window-button {
            width: 24px;
            height: 24px;
            border: none;
            border-radius: 4px;
            background: var(--border-color);
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            -webkit-user-select: none;
        }
        .window-button:hover {
            background: var(--accent-blue);
            color: #fff;
        }

        #close-btn:hover {
            background-color: #D32F2F !important;
            color: #fff !important;
        }


        .extra-buttons {
            display: flex;
            gap: 20px;
            margin-left: auto;
            margin-right: 40px;
        }
        .title-bar-button {
            padding: 5px 12px;
            background: #E0E0E0; /* pastel açık renk */
            border: none;
            border-radius: 4px;
            color: #333; /* koyu yazı */
            font-size: 12px;
            cursor: pointer;
        }
        #signup-btn {
            margin-right: 10px;
        }
        .title-bar-button:hover {
            background: #d5d5d5;
        }

        .grid-container {
            position: absolute;
            top: calc(var(--title-bar-height) + 10px);
            left: var(--gap-size);
            right: 70px; /* en sağdaki bar için boşluk */
            bottom: 60px; /* alt barda 60px yer var */
            display: grid;
            /* sol deck eski genişliğinde, orta deck genişletildi, en sağdaki can barları daraltıldı */
            grid-template-columns: 0.64fr 4px 1.2fr 4px 1fr 4px 0.8fr;
            grid-template-rows: 1fr;
            gap: var(--gap-size);
            /* Minimum genişlik ayarları - çok küçük ekranlarda düzen bozulmasını önler */
            min-width: 700px;
        }
        .resizer {
            background: var(--border-color);
            cursor: col-resize;
            width: 4px;
        }
        .resizer:hover {
            background: var(--accent-blue);
        }
        .deck {
            background: var(--card-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: var(--deck-padding);
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--border-color) var(--bg-color);
        }
            .deck > * {
            transition: all 0.4s ease-in-out;
        }

        .deck::-webkit-scrollbar {
            width: 10px;
            background-color: var(--bg-color);
        }
        .deck::-webkit-scrollbar-track {
            background-color: var(--bg-color);
        }
        .deck::-webkit-scrollbar-thumb {
            background-color: var(--border-color);
            border-radius: 6px;
            border: 2px solid var(--bg-color);
        }
        .deck::-webkit-scrollbar-thumb:hover {
            background-color: var(--accent-blue);
        }
        .left-deck {
            display: flex;
            flex-direction: column;
            gap: var(--gap-size);
        }
        .profile-selector {
            margin-bottom: 12px;
        }
        .selector-buttons {
            margin-bottom: 12px;
        }
        .platform-select {
            width: 100%;
            padding: 8px;
            border: 1px solid #B0B0B0;
            border-radius: 5px;
            background-color: #F5F5F7;
            color: #000000;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
        }
        .platform-select option {
            background-color: #F5F5F7;
            color: #000000;
            font-weight: bold;
        }
        .platform-select option:hover {
            background-color: #E0E0E5;
            color: #000000;
        }
        .profile-list {
            margin-top: 6px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .profile-item {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 13px;
            text-align: center;
        }

        .profile-item:hover {
            background: var(--accent-blue);
        }
        .middle-deck {}
        .tab-bar {
            display: flex;
            gap: 12px;
            margin-bottom: 10px;
        }
        .tab {
            color: var(--text-secondary);
            cursor: pointer;
            padding-bottom: 2px;
            font-weight: 500;
            font-size: 14px;
        }
        .tab.active {
            color: var(--text-primary);
            border-bottom: 2px solid var(--accent-blue);
        }
        .list-item {
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .thread-count {
            background: var(--accent-blue);
            color: white;
            padding: 2px 4px;
            border-radius: 4px;
            font-size: 11px;
        }

        .right-deck {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .section {
            margin-bottom: 14px;
        }
        .section-header {
            font-size: var(--section-header-size);
            color: var(--text-secondary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
            flex-wrap: nowrap;
            white-space: nowrap;
            overflow: hidden;
        }
        .badge {
            background: #373737;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        ul {
            list-style: none;
            padding-left: 0;
            margin: 6px 0;
        }
        ul li {
            position: relative;
            padding-left: 16px;
            margin-bottom: 4px;
            font-size: 14px;
        }
        ul li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: var(--accent-blue);
        }

        .bottom-bar {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--header-footer-bg);
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 50px;
        }
        .bottom-buttons {
            display: flex;
            gap: 12px;
        }
        .action-button {
            background: #292A2D;
            border: 1px solid #3A3B3E;
            padding: 8px 14px;
            border-radius: 6px;
            color: #F8F8F8;
            font-size: 14px;
            font-family: 'Segoe UI', sans-serif;
            letter-spacing: 0.4px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s, color 0.3s, border-color 0.3s;
        }
        .action-button:hover {
            background: #35363A;
            color: #FFFFFF;
            border-color: #505255;
        }
        .action-button.primary {
            background: #006400;
            color: #EAEAEA;
            font-weight: bold;
            font-size: 15px;
            letter-spacing: 0.6px;
            border: 1px solid #163A5E;
        }
        .action-button.primary:hover {
            background: #008f5a; /* daha koyu, yumuşak bir yeşil */
            border-color: #267f60; /* daha yumuşak bir kontrast */
            color: #FFFFFF;
        }

        #stopBtn {
            background: #5E1E1E;
            border: 1px solid #4A1919;
            color: #FFFFFF;
        }
        #stopBtn:hover {
            background: #C0392B;
            border-color: #A93226;
            color: #FFFFFF;
        }

        .right-side-bar {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            width: 50px;
            background-color: var(--header-footer-bg);
            border-left: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 45px;
        }
        .right-side-bar-button {
            font-size: 20px;
            background: none;
            border: none;
            color: var(--text-primary);
            cursor: pointer;
            margin-bottom: 8px;
        }
        .right-side-bar-button:hover {
            color: var(--accent-blue);
        }

        /* -- İSTATİSTİK KUTUCUKLARI İÇİN YENİ CSS -- */
.stats-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stats-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
    color: #fff;
}

.stats-update-time {
    font-size: 12px;
    color: #aaa;
}

/* === GRID DÜZENİ === */
.stats-grid {
    display: grid;
    /* Genişliğe göre otomatik kolon sayısı (120 px'den küçükse tek sütuna iner) */
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.stat-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #1e1e1e;
    padding: 10px 15px;
    border-radius: 10px;
}


.stat-label {
    font-size: 13px;
    color: #aaa;
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
}

.stat-change {
    font-size: 12px;
    font-weight: bold;
}

/* Artış = yeşil */
.stat-change.up {
    color: #4caf50;
}

/* Düşüş = kırmızı */
.stat-change.down {
    color: #f44336;
}


        .extra-deck {
            background: var(--card-bg);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: var(--deck-padding);
            overflow-y: auto;
        }

        /* Live Feed Stilleri */
        .live-feed-card {
            display: flex;
            align-items: center;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .live-feed-card:hover {
            border-color: var(--accent-blue);
            background: #252525;
        }

        .live-feed-logo {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .live-feed-username {
            font-weight: bold;
            color: var(--text-primary);
            font-size: 13px;
            min-width: 60px;
        }

        .live-feed-check {
            color: #4caf50;
            font-size: 16px;
            font-weight: bold;
            margin-left: auto;
        }

        .live-feed-progressbar {
            flex: 1;
            height: 6px;
            background: #333;
            border-radius: 3px;
            overflow: hidden;
            margin: 0 8px;
        }

        .live-feed-bar {
            height: 100%;
            background: linear-gradient(90deg, #2196F3, #21CBF3);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .live-feed-counter {
            font-size: 11px;
            color: var(--text-secondary);
            background: #333;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 30px;
            text-align: center;
        }

        .live-feed-desc {
            flex: 1;
            font-size: 12px;
            color: var(--text-secondary);
            margin: 0 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .live-feed-thumb {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            object-fit: cover;
            flex-shrink: 0;
        }

        .live-feed-skeleton {
            background: linear-gradient(-90deg, #2b2b2b 0%, #3a3a3a 50%, #2b2b2b 100%);
            background-size: 400% 400%;
            animation: shimmer 1.2s ease-in-out infinite;
            border-radius: 8px;
            height: 60px;
            width: 100%;
            margin-bottom: 8px;
        }

        .live-feed-container {
            display: flex;
            flex-direction: column;
            gap: 8px;
            transition: all 0.3s ease;
        }

        /* Inline Profile Editor Styles */
        #live-feed-container.profile-editor-mode {
            position: relative;
            padding: 20px;
            display: flex;
            flex-direction: column;
            height: 100%;
            box-sizing: border-box;
        }

        #live-feed-container button {
            transition: all 0.2s ease;
        }

        #live-feed-container input {
            transition: border-color 0.2s ease, background-color 0.2s ease;
        }

        #live-feed-container input:focus {
            border-color: #2196F3;
            background-color: #252525;
            outline: none;
        }

        .live-feed-skeleton-card {
            display: flex; flex-direction: column; gap: 8px; align-items: flex-start; background: #232323; border-radius: 10px; padding: 13px 16px; margin-bottom: 13px; width: 100%; box-sizing: border-box;
        }
        .live-feed-skeleton-logo {
            width: 36px; height: 36px; border-radius: 8px; margin-bottom: 3px; background: #313131;
        }
        .live-feed-skeleton-user {
            width: 105px; height: 17px; border-radius: 5px; background: #282828;
        }
        .live-feed-skeleton-bar {
            width: 94%; height: 14px; border-radius: 7px; background: linear-gradient(-90deg, #2b2b2b 0%, #3a3a3a 50%, #2b2b2b 100%); background-size: 400% 400%; animation: shimmer 1.2s ease-in-out infinite;
        }
        .live-feed-event-card {
            background: #232323; border-radius: 10px; margin-bottom: 13px;
            box-shadow: 0 1px 4px #0002; padding: 14px 16px; display: flex; flex-direction: column;
            width: 100%; max-width: 100%; box-sizing: border-box;
        }
        .stat-header {
            display: flex; align-items: center; gap: 6px; margin-bottom: 8px;
            flex-wrap: nowrap;
            white-space: nowrap;
            overflow: hidden;
        }
        .stat-logo {
            width: 36px; height: 36px; border-radius: 8px; object-fit: contain; background: #292929; margin-right: 4px;
        }
        .stat-username {
            font-size: 14px; font-weight: 600; color: #fff; letter-spacing: .2px; font-family: 'Segoe UI', Arial, sans-serif;
            margin-right: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .stat-counter {
            font-size: 13px; color: #a9ebc5; margin-left: auto;
        }
        .stat-checkmark {
            color: #45ff5e; font-size: 14px; margin-left: 6px; font-weight: bold;
            animation: stat-tick-pop .6s cubic-bezier(.4,1.6,.6,1) 1;
        }
        @keyframes stat-tick-pop {
            0% { transform: scale(0.2); opacity: 0; }
            55% { transform: scale(1.17); opacity: 1; }
            100% { transform: scale(1); }
        }
        .stat-progress-container {
            width: 97%; height: 14px; background: #171c17; border-radius: 8px;
            margin-top: 4px; margin-bottom: 1px; overflow: hidden;
        }
        .stat-progress-bar {
            height: 14px; background: linear-gradient(90deg,#2bc759,#baffc7);
            border-radius: 8px; transition: width 0.5s cubic-bezier(.39,1.4,.6,1);
        }
        .stat-progress-bar.animated {
            animation: progressAnimation 3s ease-in-out forwards;
        }
        @keyframes progressAnimation {
            0% { width: 0%; }
            100% { width: 100%; }
        }
        .stat-post-desc {
            font-size: 12px; color: #f6f6f6; background: #232e23; border-radius: 5px;
            padding: 6px 10px; margin: 4px 0 8px 0; font-family: 'Segoe UI', Arial, sans-serif;
            font-weight: 500; letter-spacing: .1px; line-height: 1.3;
            display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical;
            overflow: hidden; text-overflow: ellipsis; word-wrap: break-word;
        }
        .stat-post-thumb {
            width: 99%; max-width: 340px; height: 92px; border-radius: 7px;
            object-fit: cover; background: #222; box-shadow: 0 0 9px #0005;
            margin: 0 0 2px 0; position: relative;
        }
        .stat-post-thumb-container {
            position: relative; display: inline-block; width: 100%;
        }
        .stat-video-icon {
            position: absolute; bottom: 6px; left: 6px; width: 20px; height: 20px;
            background: rgba(0,0,0,0.7); border-radius: 3px; display: flex;
            align-items: center; justify-content: center; color: #fff; font-size: 12px;
        }
        .stat-status-text {
            font-size: 13px; color: #f6f6f6; background: #232e23; border-radius: 5px;
            padding: 6px 10px; margin: 0 0 8px 0; font-family: 'Segoe UI', Arial, sans-serif;
            font-weight: 500; letter-spacing: .1px; text-align: center;
            display: flex; align-items: center; justify-content: center; gap: 6px;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="title-bar">
        <div class="window-title">sorcerio</div>

    <div class="status-indicator-bar">
      <div class="status-light-bar" id="statusLight" style="animation: stopAnim 1.5s ease-in-out infinite;"></div>
    </div>

        <div class="window-buttons">
            <button class="window-button" id="min-btn" title="Simge Durumuna Küçült">–</button>
            <button class="window-button" id="max-btn" title="Büyüt">&#9744;</button> <!-- Unicode: ? -->
            <button class="window-button" id="close-btn" title="Kapat">&#10005;</button> <!-- Unicode: ? -->
        </div>
    </div>

    <div class="grid-container">
    <div class="deck left-deck">
        <div class="profile-selector">
            <div class="selector-buttons">
                <select id="platformSelect" class="platform-select">
                    <option value="instagram">Instagram</option>
                    <option value="twitter">Twitter</option>
                </select>
            </div>
            <div id="profile-list" class="profile-list"></div>
        </div>
    </div>

    <script>

    </script>


        <div class="resizer" id="resizer-1"></div>

        <div class="deck middle-deck">
    <div class="live-feed-container" id="live-feed-container">
        <div class="skeleton-container" id="middle-skeleton">
            <div class="skeleton" style="width: 80%; height: 24px;"></div>
            <div class="skeleton" style="width: 60%; height: 20px;"></div>
            <div class="skeleton" style="width: 90%; height: 24px;"></div>
            <div class="skeleton" style="width: 70%; height: 18px;"></div>
        </div>
    </div>
</div>



        <div class="resizer" id="resizer-2"></div>

        <div class="deck right-deck">
        <div class="skeleton-container" id="right-skeleton">
    <div class="skeleton" style="width: 85%; height: 45px;"></div>
    <div class="skeleton" style="width: 80%; height: 45px;"></div>
    <div class="skeleton" style="width: 75%; height: 45px;"></div>
</div>


    <!-- Twitter istatistik kutuları -->
    <div class="stats-grid twitter-stats">
        <!-- Twitter kutuları burada oluşturulacak -->
    </div>

    <!-- Instagram istatistik kutuları -->
    <div class="stats-grid instagram-stats">
        <!-- Instagram kutuları burada oluşturulacak -->
    </div>
</div>


        <div class="resizer" id="resizer-3"></div>

        <div class="deck extra-deck">
    <div class="skeleton-container" id="extra-skeleton">
        <div class="skeleton" style="height: 20px; width: 70%;"></div>
        <div class="skeleton" style="height: 14px; width: 100%;"></div>
        <div class="skeleton" style="height: 14px; width: 90%;"></div>
        <div class="skeleton" style="height: 14px; width: 80%;"></div>
    </div>
</div>

    </div>

    <!-- Orijinal bottom-bar bloğunu bu güncellenmiş haliyle değiştirin -->
    <div class="bottom-bar">
      <div class="bottom-buttons">
        <button class="action-button primary" id="startBtn">Başla</button>
        <button class="action-button"        id="stopBtn">Durdur</button>
        <button class="action-button"        id="errorBtn">DESTEK</button>
      </div>
    </div>



    <div class="right-side-bar">
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            new QWebChannel(qt.webChannelTransport, function(channel) {
                let pyBridge = channel.objects.pyBridge;
                let profileBridge = channel.objects.profileBridge;

                function setStatusBarColor(colorState) {
                    const bar = document.querySelector(".status-light-bar");
                    if (!bar) return;

                    // Mevcut animasyonları sıfırla
                    bar.style.animation = 'none';
                    bar.style.backgroundImage = 'none';

                    let gradient = '';
                    let animationName = '';
                    let duration = '1.8s';

                    if (colorState === "working") {
                        // Daha keskin geçişli yeşil akış
                        gradient = 'linear-gradient(90deg, #1e8e3e 0%, #1e8e3e 40%, #34c759 50%, #1e8e3e 60%, #1e8e3e 100%)';
                        animationName = 'flowGreen';
                    } else if (colorState === "stopped") {
                        // Daha keskin geçişli kırmızı akış
                        gradient = 'linear-gradient(90deg, #a02c2c 0%, #a02c2c 40%, #e74c3c 50%, #a02c2c 60%, #a02c2c 100%)';
                        animationName = 'flowRed';
                    } else {
                        // Gri idle akışı
                        gradient = 'linear-gradient(90deg, #444 0%, #444 40%, #666 50%, #444 60%, #444 100%)';
                        duration = '2.5s';
                    }

                    bar.style.backgroundImage = gradient;
                    if (animationName) {
                        bar.style.animation = `${animationName} ${duration} linear infinite`;
                    }

                    // Animasyonu tetikle
                    void bar.offsetWidth;
                }




                // Sayfa yüklendiğinde başlangıçta kırmızı göster
                setStatusBarColor("stopped");



                // Pencere kontrol butonları
                document.getElementById("min-btn").addEventListener("click", function() {
                    pyBridge.minimizeWindow();
                });
                document.getElementById("max-btn").addEventListener("click", function() {
                    pyBridge.maximizeWindow();
                });
                document.getElementById("close-btn").addEventListener("click", function() {
                    pyBridge.closeWindow();
                });
                document.getElementById("startBtn").addEventListener("click", function() {
                  pyBridge.startProcessingFromJS();
                  setStatusBarColor("working");
                });

                document.getElementById("stopBtn").addEventListener("click", function() {
                  pyBridge.stopProcessing();
                  setStatusBarColor("stopped");
                });






                // Başlık çubuğu sürükleme
                let titleBar = document.querySelector(".title-bar");
                let isDraggingWindow = false;
                titleBar.addEventListener("mousedown", function(e) {
                    if (e.target.closest('.window-buttons') || e.target.closest('.extra-buttons')) {
                        return;
                    }
                    isDraggingWindow = true;
                    pyBridge.startDrag();
                });
                document.addEventListener("mouseup", function(e) {
                    if (isDraggingWindow) {
                        isDraggingWindow = false;
                        pyBridge.stopDrag();
                    }
                });
                document.addEventListener("mousemove", function(e) {
                    if (isDraggingWindow) {
                        pyBridge.dragWindow();
                    }
                });

                // Resizerlar
                const resizer1 = document.getElementById('resizer-1');
                const resizer2 = document.getElementById('resizer-2');
                const resizer3 = document.getElementById('resizer-3');
                const gridContainer = document.querySelector('.grid-container');
                let isResizing = false;
                let currentResizer = null;
                let startX = 0;
                let leftColWidth = 0;
                let middleColWidth = 0;
                let rightColWidth = 0;
                let extraColWidth = 0;

                function getPxWidths() {
                    const leftDeck = document.querySelector('.left-deck');
                    const middleDeck = document.querySelector('.middle-deck');
                    const rightDeck = document.querySelector('.right-deck');
                    const extraDeck = document.querySelector('.extra-deck');
                    let leftRect = leftDeck.getBoundingClientRect();
                    let midRect = middleDeck.getBoundingClientRect();
                    let rightRect = rightDeck.getBoundingClientRect();
                    let extraRect = extraDeck.getBoundingClientRect();
                    leftColWidth = leftRect.width;
                    middleColWidth = midRect.width;
                    rightColWidth = rightRect.width;
                    extraColWidth = extraRect.width;
                }
                function setGridColumns(l, m, r, e) {
                    gridContainer.style.gridTemplateColumns = l + 'px 4px ' + m + 'px 4px ' + r + 'px 4px ' + e + 'px';
                }
                function mouseDownHandler(e, whichResizer) {
                    isResizing = true;
                    currentResizer = whichResizer;
                    startX = e.clientX;
                    getPxWidths();
                    document.addEventListener('mousemove', mouseMoveHandler);
                    document.addEventListener('mouseup', mouseUpHandler);
                    e.preventDefault();
                }
                function mouseMoveHandler(e) {
                    if (!isResizing) return;
                    let dx = e.clientX - startX;
                    let minW = 100;
                    if (currentResizer === resizer1) {
                        let newLeft = leftColWidth + dx;
                        let newMid = middleColWidth - dx;
                        if (newLeft < minW) {
                            newLeft = minW;
                            newMid = leftColWidth + middleColWidth - minW;
                        }
                        if (newMid < minW) {
                            newMid = minW;
                            newLeft = leftColWidth + middleColWidth - minW;
                        }
                        setGridColumns(newLeft, newMid, rightColWidth, extraColWidth);
                    } else if (currentResizer === resizer2) {
                        let newMid = middleColWidth + dx;
                        let newRight = rightColWidth - dx;
                        if (newMid < minW) {
                            newMid = minW;
                            newRight = middleColWidth + rightColWidth - minW;
                        }
                        if (newRight < minW) {
                            newRight = minW;
                            newMid = middleColWidth + rightColWidth - minW;
                        }
                        setGridColumns(leftColWidth, newMid, newRight, extraColWidth);
                    } else if (currentResizer === resizer3) {
                        let newRight = rightColWidth + dx;
                        let newExtra = extraColWidth - dx;
                        if (newRight < minW) {
                            newRight = minW;
                            newExtra = rightColWidth + extraColWidth - minW;
                        }
                        if (newExtra < minW) {
                            newExtra = minW;
                            newRight = rightColWidth + extraColWidth - minW;
                        }
                        setGridColumns(leftColWidth, middleColWidth, newRight, newExtra);
                    }
                }
                function mouseUpHandler() {
                    isResizing = false;
                    currentResizer = null;
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                }
                resizer1.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer1));
                resizer2.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer2));
                resizer3.addEventListener('mousedown', (e) => mouseDownHandler(e, resizer3));

                // Platform seçimi (dropdown)
                const platformSelect = document.getElementById("platformSelect");
                const profileListDiv = document.getElementById("profile-list");

                window.profileItems = [];

                function updateProfileList(response) {
                    // 1) JSON yanıtını işle
                    let profiles = JSON.parse(response);
                    window.profileItems = profiles;

                    // 2) Eski profil öğelerini temizle
                    const profileListDiv = document.getElementById("profile-list");
                    profileListDiv.innerHTML = "";

                    // 3) Yeni profilleri ekle
                    profiles.forEach(function(p) {
                        let div = document.createElement("div");
                        div.className = "profile-item";
                        div.textContent = p.displayName;
                        div.setAttribute("data-profile-path", p.path);
                        div.addEventListener("click", function() {
                            profileBridge.showProfileEditorInline(p.path);
                        });
                        profileListDiv.appendChild(div);
                    });
                }


                window.updateProfileItemText = function(path, newName) {
                    // 1) İç veriyi güncelle
                    window.profileItems.forEach(item => {
                        if (item.path === path) {
                            item.displayName = newName;
                        }
                    });
                    // 2) DOM'daki öğeyi güncelle
                    document.querySelectorAll('.profile-item').forEach(el => {
                        if (el.getAttribute('data-profile-path') === path) {
                            el.textContent = newName;
                        }
                    });
                };

                // Profil listesini yenileme fonksiyonu - Python'daki sync fonksiyonu ile bağlantılı
                window.refreshProfilesList = function() {
                    if (document.getElementById("platformSelect")) {
                        let selected = document.getElementById("platformSelect").value;
                        // Önce profil listesini gösteren iskelet
                        const profileListDiv = document.getElementById("profile-list");
                        if (profileListDiv) {
                            profileListDiv.innerHTML = '<div class="skeleton" style="width: 90%; height: 32px;"></div><div class="skeleton" style="width: 80%; height: 32px;"></div><div class="skeleton" style="width: 95%; height: 32px;"></div>';
                        }
                        // Profil listesini yenile
                        profileBridge.getProfiles(selected, function(response) {
                            updateProfileList(response);
                        });
                    }
                };

                platformSelect.addEventListener("change", function() {
                    let selected = platformSelect.value;
                    // Platform değiştiğinde önce profil-JSON senkronizasyonunu çağır
                    // Bu yapı JS -> Python -> JS şeklinde bir döngü oluşturur
                    try {
                        // Directly call the Python method through pyBridge
                        if (pyBridge && typeof pyBridge.syncProfilesWithJSON === 'function') {
                            pyBridge.syncProfilesWithJSON();
                        }
                    } catch (e) {
                        console.log("Profile sync not available yet:", e);
                    }
                    profileBridge.getProfiles(selected, function(response) {
                        updateProfileList(response);
                    });
                });

                // Sayfa yüklenince varsayılan "instagram" profilleri göster
                platformSelect.value = "instagram";
                profileBridge.getProfiles("instagram", function(response) {
                    updateProfileList(response);
                });

                // --------------------------------------------------
                // DESTEK BUTONU TIKLANDIĞINDA PYTHONDAKİ FONKSİYON ÇAĞRILACAK
                // --------------------------------------------------
                document.getElementById("errorBtn").addEventListener("click", function() {
                    pyBridge.openDestekWindow();
                });

                // --------------------------------------------------
                // INLINE PROFILE EDITOR FUNCTIONS
                // --------------------------------------------------
                window.setupInlineProfileEditor = function(profilePath) {
                    try {
                        // Close button handlers with error handling
                        const closeBtn = document.getElementById("inline-close-btn");
                        const closeBtn2 = document.getElementById("inline-close-btn-2");

                        if (closeBtn) {
                            closeBtn.addEventListener("click", function() {
                                try {
                                    profileBridge.hideProfileEditorInline();
                                } catch (e) {
                                    console.error("Error hiding profile editor:", e);
                                }
                            });
                        }

                        if (closeBtn2) {
                            closeBtn2.addEventListener("click", function() {
                                try {
                                    profileBridge.hideProfileEditorInline();
                                } catch (e) {
                                    console.error("Error hiding profile editor:", e);
                                }
                            });
                        }

                        // Save button handler with error handling
                        const saveBtn = document.getElementById("inline-save-btn");
                        if (saveBtn) {
                            saveBtn.addEventListener("click", function() {
                                try {
                                    const username = document.getElementById("inline-username").value || "";
                                    const password = document.getElementById("inline-password").value || "";
                                    const hashtags = document.getElementById("inline-hashtags").value || "";

                                    profileBridge.saveProfileInline(profilePath, username, password, hashtags);
                                } catch (e) {
                                    console.error("Error saving profile:", e);
                                    // Show error in info label
                                    const infoLabel = document.getElementById("inline-info-label");
                                    if (infoLabel) {
                                        infoLabel.textContent = "HATA: Kaydetme başarısız";
                                        infoLabel.style.color = "#FF0000";
                                        infoLabel.style.fontWeight = "bold";
                                        setTimeout(() => {
                                            infoLabel.textContent = "";
                                        }, 3000);
                                    }
                                }
                            });
                        }

                    // Username change handler for profile name update
                    const usernameInput = document.getElementById("inline-username");
                    const profileNameDiv = document.getElementById("inline-profile-name");
                    // Default profil adı (profile1, profile2 vs) dosya adından alınacak
                    let defaultProfileName = "";
                    if (profilePath) {
                        const parts = profilePath.split(/[\\/]/);
                        const file = parts[parts.length - 1];
                        defaultProfileName = file.replace(/\.json$/, "");
                    }
                    // İlk açılışta kutu boşsa default ismi göster
                    if (profileNameDiv && (!profileNameDiv.textContent.trim() || profileNameDiv.textContent === " ")) {
                        profileNameDiv.textContent = defaultProfileName;
                        profileNameDiv.style.cssText = `
                            color: black;
                            background: linear-gradient(135deg, #ffe082 0%, #fff9c4 50%, #ffd54f 100%);
                            border: 1px solid #d6a300;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-family: 'Segoe UI', 'Arial', sans-serif;
                            font-size: 16px;
                            font-weight: bold;
                            letter-spacing: 0.2px;
                            display: inline-block;
                            min-width: 70px;
                            text-align: center;
                            transition: min-width 0.1s, width 0.1s;
                        `;
                    }
                    if (usernameInput) {
                        usernameInput.addEventListener("input", function() {
                            const newUsername = this.value.trim();
                            if (profileNameDiv) {
                                if (newUsername) {
                                    profileNameDiv.textContent = newUsername;
                                    // Dinamik genişlik için geçici bir span ile ölçüm
                                    const tempSpan = document.createElement('span');
                                    tempSpan.style.visibility = 'hidden';
                                    tempSpan.style.position = 'absolute';
                                    tempSpan.style.font = "16px 'Segoe UI', 'Arial', sans-serif";
                                    tempSpan.style.fontWeight = 'bold';
                                    tempSpan.style.letterSpacing = '0.2px';
                                    tempSpan.textContent = newUsername;
                                    document.body.appendChild(tempSpan);
                                    let width = tempSpan.offsetWidth + 24; // padding dahil
                                    if (width < 70) width = 70;
                                    profileNameDiv.style.cssText = `
                                        color: black;
                                        background: linear-gradient(135deg, #ffe082 0%, #fff9c4 50%, #ffd54f 100%);
                                        border: 1px solid #d6a300;
                                        border-radius: 6px;
                                        padding: 6px 12px;
                                        font-family: 'Segoe UI', 'Arial', sans-serif;
                                        font-size: 16px;
                                        font-weight: bold;
                                        letter-spacing: 0.2px;
                                        display: inline-block;
                                        min-width: 70px;
                                        width: ${width}px;
                                        text-align: center;
                                        transition: min-width 0.1s, width 0.1s;
                                    `;
                                    document.body.removeChild(tempSpan);
                                } else {
                                    profileNameDiv.textContent = defaultProfileName;
                                    profileNameDiv.style.cssText = `
                                        color: black;
                                        background: linear-gradient(135deg, #ffe082 0%, #fff9c4 50%, #ffd54f 100%);
                                        border: 1px solid #d6a300;
                                        border-radius: 6px;
                                        padding: 6px 12px;
                                        font-family: 'Segoe UI', 'Arial', sans-serif;
                                        font-size: 16px;
                                        font-weight: bold;
                                        letter-spacing: 0.2px;
                                        display: inline-block;
                                        min-width: 70px;
                                        width: 70px;
                                        text-align: center;
                                        transition: min-width 0.1s, width 0.1s;
                                    `;
                                }
                            }
                        });
                    }

                        // Time Settings button handler with error handling
                        const timeSettingsBtn = document.getElementById("inline-time-settings-btn");
                        if (timeSettingsBtn) {
                            timeSettingsBtn.addEventListener("click", function() {
                                try {
                                    // Show time settings dialog as popup
                                    profileBridge.showTimeSettingsInStats(profilePath);
                                } catch (e) {
                                    console.error("Error opening time settings:", e);
                                }
                            });
                        }

                        // Link Add button handler with error handling
                        const linkAddBtn = document.getElementById("inline-link-add-btn");
                        if (linkAddBtn) {
                            linkAddBtn.addEventListener("click", function() {
                                try {
                                    // Show link editor dialog as popup
                                    profileBridge.showLinkEditorInStats(profilePath);
                                } catch (e) {
                                    console.error("Error opening link editor:", e);
                                }
                            });
                        }

                    } catch (e) {
                        console.error("Error setting up inline profile editor:", e);
                    }

                    // Add hover effects for buttons
                    const buttons = document.querySelectorAll('#live-feed-container button');
                    buttons.forEach(button => {
                        button.addEventListener('mouseenter', function() {
                            if (this.id !== 'inline-close-btn') {
                                this.style.backgroundColor = '#2196F3';
                            } else {
                                this.style.backgroundColor = '#FF4444';
                            }
                        });

                        button.addEventListener('mouseleave', function() {
                            if (this.id !== 'inline-close-btn') {
                                this.style.backgroundColor = '#242424';
                            } else {
                                this.style.backgroundColor = '#D32F2F';
                            }
                        });
                    });
                };






            });
        });
    </script>
</body>
</html>
"""
dummy_module = types.ModuleType("moviepy.editor")


class DummyVideoFileClip:
    def __init__(self, path):
        self.path = path
        self.duration = dummy_duration(path)
        try:
            cap = cv2.VideoCapture(self.path)
            if cap.isOpened():
                width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
                height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
                self.size = (int(width), int(height))
            else:
                self.size = (640, 480)
            cap.release()
        except Exception:
            self.size = (640, 480)

    def save_frame(self, filename, t=0, withmask=True, **kwargs):
        cap = cv2.VideoCapture(self.path)
        if not cap.isOpened():
            cap.release()
            raise Exception("Video açılamadı.")
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps == 0:
            cap.release()
            raise Exception("FPS değeri 0.")
        frame_index = int(t * fps)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
        ret, frame = cap.read()
        if ret:
            cv2.imwrite(filename, frame)
        else:
            raise Exception("Belirtilen zamanda kare yakalanamadı.")
        cap.release()

    def close(self):
        pass


def setup_configurations():
    """Konfigürasyon dosyalarını kullanıcıya özel dizinde oluşturur/yükler.
       Eğer kullanıcı dizininde yoksa, paketlenmiş varsayılanları kopyalar.
    """
    logger = logging.getLogger() # Mevcut logger'ı al
    # Kullanıcı konfigürasyonlarının saklanacağı alt dizin
    # USER_CONFIG_DIR dosyanın başında tanımlanmış global bir Path nesnesi olmalı.
    user_config_subdir = USER_CONFIG_DIR / "configuration"

    # Yönetilecek konfigürasyon dosyaları ve onların paket içindeki göreceli yolları
    # (proje kökünden itibaren, örn: "configuration/profiles.json")
    config_files_map = {
        "config.json": "configuration/config.json",
        "profiles.json": "configuration/profiles.json",
        "scheduler.json": "configuration/scheduler.json",
        "stats_config.json": "configuration/stats_config.json",
        "theme.json": "configuration/theme.json",
        "user_agents.json": "configuration/user_agents.json"
    }

    # Her bir konfigürasyon dosyasını işle
    resolved_config_paths = {}
    for filename, resource_rel_path in config_files_map.items():
        user_file_abs_path = user_config_subdir / filename
        # _ensure_user_file_or_copy_default fonksiyonu zaten dizin oluşturma ve kopyalamayı yapar.
        resolved_path = _ensure_user_file_or_copy_default(user_file_abs_path, resource_rel_path)
        resolved_config_paths[filename] = resolved_path
        # logger.info(f"Konfigürasyon dosyası için yol ayarlandı: {filename} -> {resolved_path}") # Bu çok fazla log üretebilir.

    # profiles.json için varsayılan içerik oluşturma (eğer dosya boş veya yoksa)
    profiles_json_path = resolved_config_paths["profiles.json"]
    if not profiles_json_path.exists() or profiles_json_path.stat().st_size == 0:
        logger.warning(f"Kullanıcı profilleri dosyası ({profiles_json_path}) boş veya bulunamadı. Varsayılan yapı oluşturuluyor.")
        default_profiles_content = {"instagram": {}, "youtube": {}, "twitter": {}}
        try:
            with open(profiles_json_path, 'w', encoding='utf-8') as f:
                json.dump(default_profiles_content, f, indent=4)
            logger.info(f"Varsayılan boş profiller dosyası oluşturuldu: {profiles_json_path}")
        except Exception as e:
            logger.error(f"Varsayılan profiller dosyası ({profiles_json_path}) oluşturulurken hata: {e}", exc_info=True)
    
    # config.json için varsayılan içerik oluşturma
    config_json_path = resolved_config_paths["config.json"]
    if not config_json_path.exists() or config_json_path.stat().st_size == 0:
        logger.warning(f"Ana yapılandırma dosyası ({config_json_path}) boş veya bulunamadı. Varsayılan yapı oluşturuluyor.")
        default_config_content = {"ffmpeg_path": "", "theme": "default"}
        try:
            with open(config_json_path, 'w', encoding='utf-8') as f:
                json.dump(default_config_content, f, indent=4)
            logger.info(f"Varsayılan ana yapılandırma dosyası oluşturuldu: {config_json_path}")
        except Exception as e:
            logger.error(f"Varsayılan ana yapılandırma dosyası ({config_json_path}) oluşturulurken hata: {e}", exc_info=True)
            
    # scheduler.json için varsayılan içerik (boş sözlük)
    scheduler_json_path = resolved_config_paths["scheduler.json"]
    if not scheduler_json_path.exists() or scheduler_json_path.stat().st_size == 0:
        try:
            with open(scheduler_json_path, 'w', encoding='utf-8') as f:
                json.dump({}, f, indent=4)
            logger.info(f"Varsayılan zamanlayıcı dosyası oluşturuldu: {scheduler_json_path}")
        except Exception as e:
            logger.error(f"Varsayılan zamanlayıcı dosyası ({scheduler_json_path}) oluşturulurken hata: {e}", exc_info=True)

    # stats_config.json için varsayılan içerik
    stats_config_json_path = resolved_config_paths["stats_config.json"]
    if not stats_config_json_path.exists() or stats_config_json_path.stat().st_size == 0:
        default_stats_content = {"enabled": True, "send_interval_hours": 24}
        try:
            with open(stats_config_json_path, 'w', encoding='utf-8') as f:
                json.dump(default_stats_content, f, indent=4)
            logger.info(f"Varsayılan istatistik yapılandırma dosyası oluşturuldu: {stats_config_json_path}")
        except Exception as e:
            logger.error(f"Varsayılan istatistik yapılandırma dosyası ({stats_config_json_path}) oluşturulurken hata: {e}", exc_info=True)
    
    # theme.json ve user_agents.json dosyaları genellikle varsayılan olarak dolu gelir,
    # _ensure_user_file_or_copy_default ile kopyalanmaları yeterlidir.
    # Ekstra kontrol gerekirse buraya eklenebilir.

    logger.info(f"Tüm konfigürasyonlar kullanıcı dizininde ayarlandı/kontrol edildi: {user_config_subdir}")
    return resolved_config_paths # Çözümlenmiş yolları döndür


def resource_path(relative_path):
    """
    Get absolute path to resource, works for dev and for PyInstaller.
    This function helps locate bundled resources in both development and packaged environments.
    """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        # Development mode - use the directory where this script is located
        base_path = os.path.dirname(os.path.abspath(__file__))

    return os.path.join(base_path, relative_path)


def get_logo_path(platform):
    """
    Get the path to the logo file for the specified platform.

    Args:
        platform (str): Platform name ('instagram', 'twitter', 'youtube', etc.)

    Returns:
        str: Path to the logo file
    """
    try:
        # Map platform names to logo files
        logo_mapping = {
            'instagram': 'instagram.ico',
            'twitter': 'x.ico',
            'x': 'x.ico',  # Alternative name for Twitter
            'youtube': 'instagram.ico',  # Fallback to Instagram logo for YouTube
            'default': 'instagram.ico'  # Default fallback
        }

        # Get the logo filename for the platform
        logo_filename = logo_mapping.get(platform.lower(), logo_mapping['default'])

        # Get the full path using resource_path
        logo_path = resource_path(logo_filename)

        # Verify the file exists, if not use default
        if not os.path.exists(logo_path):
            logging.warning(f"Logo file not found for platform '{platform}': {logo_path}")
            # Try default logo
            default_logo_path = resource_path(logo_mapping['default'])
            if os.path.exists(default_logo_path):
                return default_logo_path
            else:
                # Return empty string if no logo is available
                logging.error(f"Default logo file not found: {default_logo_path}")
                return ""

        return logo_path

    except Exception as e:
        logging.error(f"Error getting logo path for platform '{platform}': {e}")
        return ""


def detect_platform_key():
    """
    Detect the current platform and return appropriate key for Chromium downloads.

    Returns:
        str: Platform key ('win32', 'win64', 'mac', 'mac_arm', 'linux_x64')
    """
    import platform
    import sys

    try:
        system = platform.system().lower()
        machine = platform.machine().lower()

        if system == "windows":
            # Check if 64-bit or 32-bit
            if "64" in machine or sys.maxsize > 2**32:
                return "win64"
            else:
                return "win32"
        elif system == "darwin":  # macOS
            # Check if Apple Silicon (M1/M2) or Intel
            if machine in ["arm64", "aarch64"]:
                return "mac_arm"
            else:
                return "mac"
        elif system == "linux":
            return "linux_x64"
        else:
            # Default fallback
            logging.warning(f"Unknown platform: {system}, defaulting to win64")
            return "win64"

    except Exception as e:
        logging.error(f"Error detecting platform: {e}")
        return "win64"  # Safe default


# Chromium download links for different platforms
chromium_links = {
    "win32": {
        "browser": "https://commondatastorage.googleapis.com/chromium-browser-snapshots/Win/1108766/chrome-win.zip",
        "driver": "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_win32.zip"
    },
    "win64": {
        "browser": "https://commondatastorage.googleapis.com/chromium-browser-snapshots/Win_x64/1108766/chrome-win.zip",
        "driver": "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_win32.zip"
    },
    "mac": {
        "browser": "https://commondatastorage.googleapis.com/chromium-browser-snapshots/Mac/1108766/chrome-mac.zip",
        "driver": "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_mac64.zip"
    },
    "mac_arm": {
        "browser": "https://commondatastorage.googleapis.com/chromium-browser-snapshots/Mac_Arm/1108766/chrome-mac.zip",
        "driver": "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_mac_arm64.zip"
    },
    "linux_x64": {
        "browser": "https://commondatastorage.googleapis.com/chromium-browser-snapshots/Linux_x64/1108766/chrome-linux.zip",
        "driver": "https://chromedriver.storage.googleapis.com/114.0.5735.90/chromedriver_linux64.zip"
    }
}


class LiveFeedEmitter:
    """
    Live feed event emitter for UI updates.
    This is a simple implementation that can be extended with PyQt signals.
    """
    def __init__(self):
        self.events = []
        self.add_item_to_feed_signal = None  # Will be set by UI

    def emit_event(self, event_data):
        """Emit a live feed event"""
        try:
            self.events.append(event_data)
            # Keep only last 50 events to prevent memory issues
            if len(self.events) > 50:
                self.events = self.events[-50:]

            # If signal is connected, emit it
            if hasattr(self, 'add_item_to_feed_signal') and self.add_item_to_feed_signal:
                self.add_item_to_feed_signal.emit(event_data)

        except Exception as e:
            logging.error(f"Error emitting live feed event: {e}")


# Global live feed emitter instance
feed_emitter = LiveFeedEmitter()


def update_feed_event(event_id, updated_data):
    """
    Update an existing feed event with new data.

    Args:
        event_id (str): ID of the event to update
        updated_data (dict): New data to update the event with
    """
    try:
        # Find and update the event in the global emitter
        for i, event in enumerate(feed_emitter.events):
            if event.get("id") == event_id:
                # Update the event with new data
                feed_emitter.events[i].update(updated_data)

                # Emit the updated event if signal is available
                if hasattr(feed_emitter, 'add_item_to_feed_signal') and feed_emitter.add_item_to_feed_signal:
                    feed_emitter.add_item_to_feed_signal.emit(feed_emitter.events[i])
                break

    except Exception as e:
        logging.error(f"Error updating feed event {event_id}: {e}")


def get_skeleton_html():
    """
    Generate skeleton HTML for loading animations in the live feed.

    Returns:
        str: HTML string for skeleton loading animation
    """
    return '''
    <div class="live-feed-skeleton-card">
        <div class="live-feed-skeleton-logo"></div>
        <div class="live-feed-skeleton-user"></div>
        <div class="live-feed-skeleton-bar"></div>
    </div>
    '''


def format_live_feed_event(event):
    """
    Format a live feed event into HTML for display.

    Args:
        event (dict): Event data containing type, platform, username, etc.

    Returns:
        str: Formatted HTML string for the event
    """
    try:
        if not event or not isinstance(event, dict):
            return ""

        event_type = event.get("type", "")
        platform = event.get("platform", "")
        username = event.get("username", "")
        logo_path = event.get("logo_path", "")
        status = event.get("status", "")
        progress = event.get("progress", 0)

        # Create platform logo HTML
        logo_html = ""
        if logo_path and os.path.exists(logo_path):
            # Convert to base64 for embedding
            try:
                import base64
                with open(logo_path, 'rb') as f:
                    logo_data = base64.b64encode(f.read()).decode()
                logo_html = f'<img src="data:image/x-icon;base64,{logo_data}" style="width: 36px; height: 36px; border-radius: 8px;">'
            except:
                logo_html = f'<div style="width: 36px; height: 36px; border-radius: 8px; background: #444;"></div>'
        else:
            logo_html = f'<div style="width: 36px; height: 36px; border-radius: 8px; background: #444;"></div>'

        # Format based on event type
        if event_type == "download_start":
            status_text = "İçerik İndiriliyor..."
            status_color = "#FFA726"
        elif event_type == "download_complete":
            status_text = "İçerik İndirildi ✓"
            status_color = "#4CAF50"
        elif event_type == "upload_post":
            status_text = "Paylaşım Yapıldı ✓"
            status_color = "#4CAF50"
        else:
            status_text = status or "İşleniyor..."
            status_color = "#2196F3"

        # Progress bar HTML
        progress_html = ""
        if progress > 0:
            progress_html = f'''
            <div style="width: 100%; height: 4px; background: #333; border-radius: 2px; margin-top: 4px;">
                <div style="width: {progress}%; height: 100%; background: {status_color}; border-radius: 2px; transition: width 0.3s ease;"></div>
            </div>
            '''

        # Main event HTML
        event_html = f'''
        <div class="live-feed-event" style="display: flex; flex-direction: column; gap: 8px; background: #232323; border-radius: 10px; padding: 13px 16px; margin-bottom: 13px;">
            <div style="display: flex; align-items: center; gap: 12px;">
                {logo_html}
                <div style="flex: 1;">
                    <div style="color: white; font-weight: bold; font-size: 14px;">{username}</div>
                    <div style="color: {status_color}; font-size: 12px; margin-top: 2px;">{status_text}</div>
                </div>
            </div>
            {progress_html}
        </div>
        '''

        return event_html

    except Exception as e:
        logging.error(f"Error formatting live feed event: {e}")
        return ""


class LiveFeedManager:
    """
    Manager for live feed events and display.
    """
    def __init__(self):
        self.events = []
        self.max_events = 50
        # Create a dummy signal object that can be connected to
        self.feed_updated = type('Signal', (), {
            'connect': lambda self, func: setattr(self, '_connected_func', func),
            'emit': lambda self, *args: getattr(self, '_connected_func', lambda *x: None)(*args),
            '_connected_func': None
        })()

    def add_event(self, event):
        """Add a new event to the feed"""
        try:
            self.events.append(event)
            # Keep only the most recent events
            if len(self.events) > self.max_events:
                self.events = self.events[-self.max_events:]

            # Emit signal to update UI
            if hasattr(self.feed_updated, '_connected_func') and self.feed_updated._connected_func:
                self.feed_updated.emit(self.events)
        except Exception as e:
            logging.error(f"Error adding event to live feed: {e}")

    def get_events(self):
        """Get all current events"""
        return self.events.copy()

    def clear_events(self):
        """Clear all events"""
        self.events.clear()
        # Emit signal to update UI
        if hasattr(self.feed_updated, '_connected_func') and self.feed_updated._connected_func:
            self.feed_updated.emit(self.events)

    def is_empty(self):
        """Check if there are no events"""
        return len(self.events) == 0

    def cleanup_old_thumbnails(self):
        """Clean up old thumbnail files from live feed events"""
        try:
            import tempfile
            import glob

            temp_dir = tempfile.gettempdir()
            # Clean up old event thumbnails (older than 24 hours)
            pattern = os.path.join(temp_dir, "event_thumb_*")
            current_time = time.time()

            for thumb_file in glob.glob(pattern):
                try:
                    file_age = current_time - os.path.getmtime(thumb_file)
                    if file_age > 86400:  # 24 hours
                        os.remove(thumb_file)
                        logging.debug(f"Old event thumbnail cleaned up: {thumb_file}")
                except Exception as e:
                    logging.debug(f"Could not clean up thumbnail {thumb_file}: {e}")

            # Also clean up live_feed_thumbnails directory if it exists
            live_feed_thumbs_dir = "live_feed_thumbnails"
            if os.path.exists(live_feed_thumbs_dir):
                for thumb_file in os.listdir(live_feed_thumbs_dir):
                    thumb_path = os.path.join(live_feed_thumbs_dir, thumb_file)
                    try:
                        file_age = current_time - os.path.getmtime(thumb_path)
                        if file_age > 86400:  # 24 hours
                            os.remove(thumb_path)
                            logging.debug(f"Old live feed thumbnail cleaned up: {thumb_path}")
                    except Exception as e:
                        logging.debug(f"Could not clean up live feed thumbnail {thumb_path}: {e}")

        except Exception as e:
            logging.error(f"Error during thumbnail cleanup: {e}")


# Global live feed manager instance
live_feed_manager = LiveFeedManager()


def reload_scheduler_helper():
    """
    Helper function to reload the scheduler.
    This is a placeholder that can be implemented based on specific needs.
    """
    try:
        logging.info("Scheduler reload requested")
        # This function should be implemented based on the specific scheduler being used
        # For now, it's a placeholder that logs the request
        return True
    except Exception as e:
        logging.error(f"Error reloading scheduler: {e}")
        return False


# Telegram configuration constants
TELEGRAM_BOT_TOKEN = ""  # To be configured by user
TELEGRAM_CHAT_ID = ""    # To be configured by user
