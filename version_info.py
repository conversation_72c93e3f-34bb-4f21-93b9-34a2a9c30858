# Version information for Sorcerio executable
# This file is used by PyInstaller to embed version info in the executable

from PyInstaller.utils.win32.versioninfo import (
    VSVersionInfo, FixedFileInfo, StringFileInfo, StringTable,
    StringStruct, VarFileInfo, VarStruct
)

version_info = VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
        StringTable(
          u'040904B0',
          [
            StringStruct(u'CompanyName', u'Ozmorph'),
            StringStruct(u'FileDescription', u'Sorcerio - Social Media Automation Tool'),
            StringStruct(u'FileVersion', u'*******'),
            StringStruct(u'InternalName', u'Sorcerio'),
            StringStruct(u'LegalCopyright', u'Copyright (C) 2024 Ozmorph. All rights reserved.'),
            StringStruct(u'OriginalFilename', u'Sorcerio.exe'),
            StringStruct(u'ProductName', u'Sorcerio'),
            StringStruct(u'ProductVersion', u'*******'),
            StringStruct(u'LegalTrademarks', u'Sorcerio is a trademark of Ozmorph'),
            StringStruct(u'Comments', u'Professional social media automation software')
          ]
        )
      ]
    ),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
